{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { computed, ref, onMounted, onUnmounted } from 'vue';\nimport { useStore } from 'vuex';\nimport { useRouter, useRoute } from 'vue-router';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { House, Folder, FolderOpened, Plus, DocumentChecked, UserFilled, User, Setting, ArrowDown, ArrowRight, SwitchButton, ChatDotRound, ChatLineRound, List, Document, Briefcase, Star, Expand, Fold, Location, Monitor } from '@element-plus/icons-vue';\nimport { getAvatarUrl, getInitial } from '@/utils/avatar';\nimport AdminNavigation from '@/components/AdminNavigation.vue';\n// import NotificationCenter from '@/components/NotificationCenter.vue'\n\nexport default {\n  name: 'DashboardView',\n  components: {\n    AdminNavigation,\n    House,\n    Folder,\n    FolderOpened,\n    Plus,\n    DocumentChecked,\n    UserFilled,\n    User,\n    Setting,\n    ArrowDown,\n    ArrowRight,\n    SwitchButton,\n    ChatDotRound,\n    ChatLineRound,\n    List,\n    Document,\n    Briefcase,\n    Star,\n    Expand,\n    Fold,\n    Location,\n    Monitor\n    // NotificationCenter\n  },\n  setup() {\n    const store = useStore();\n    const router = useRouter();\n    const route = useRoute();\n    const showUserDropdown = ref(false);\n    const sidebarCollapsed = ref(false);\n\n    // 各个菜单的展开状态，默认都展开\n    const projectMenuExpanded = ref(true);\n    const teamMenuExpanded = ref(true);\n    const collaborationMenuExpanded = ref(true);\n    const settingsMenuExpanded = ref(true);\n    const currentUser = computed(() => store.getters.currentUser);\n    const isAdmin = computed(() => store.getters.isAdmin);\n    const isTeacher = computed(() => store.getters.isTeacher);\n    const isStudent = computed(() => store.getters.isStudent);\n    const toggleSidebar = () => {\n      sidebarCollapsed.value = !sidebarCollapsed.value;\n    };\n\n    // 各个菜单的切换方法\n    const toggleProjectMenu = () => {\n      projectMenuExpanded.value = !projectMenuExpanded.value;\n    };\n    const toggleTeamMenu = () => {\n      teamMenuExpanded.value = !teamMenuExpanded.value;\n    };\n    const toggleCollaborationMenu = () => {\n      collaborationMenuExpanded.value = !collaborationMenuExpanded.value;\n    };\n    const toggleSettingsMenu = () => {\n      settingsMenuExpanded.value = !settingsMenuExpanded.value;\n    };\n    const getPageTitle = () => {\n      const routeMap = {\n        '/dashboard': '仪表板',\n        '/dashboard/projects': '项目列表',\n        '/dashboard/my-projects': '我的项目',\n        '/dashboard/projects/create': '创建项目',\n        '/dashboard/teams': '团队列表',\n        '/dashboard/my-teams': '我的团队',\n        '/dashboard/teams/create': '创建团队',\n        '/dashboard/review': '申请审核',\n        '/dashboard/profile': '个人设置',\n        '/dashboard/files': '文件管理',\n        '/dashboard/evaluation-center': '评价中心',\n        '/dashboard/collaboration/discussion': '项目讨论',\n        '/dashboard/collaboration/tasks': '任务管理',\n        '/dashboard/collaboration/space': '协作空间',\n        '/dashboard/task-publish': '发布任务',\n        '/dashboard/task-review': '任务审核',\n        // 管理员页面\n        '/dashboard/admin': '管理仪表板',\n        '/dashboard/admin/users': '用户管理',\n        '/dashboard/admin/projects': '项目管理',\n        '/dashboard/admin/teams': '团队管理',\n        '/dashboard/admin/settings': '系统设置',\n        '/dashboard/admin/logs': '系统日志'\n      };\n      return routeMap[route.path] || '项目协作平台';\n    };\n    const getPageSubtitle = () => {\n      const subtitleMap = {\n        '/dashboard': '欢迎回来，查看您的项目概览',\n        '/dashboard/projects': '浏览所有可用的项目',\n        '/dashboard/my-projects': '管理您创建的项目',\n        '/dashboard/projects/create': '创建新的协作项目',\n        '/dashboard/teams': '查看所有团队信息',\n        '/dashboard/my-teams': '管理您的团队',\n        '/dashboard/teams/create': '组建新的项目团队',\n        '/dashboard/review': '审核学生的申请',\n        '/dashboard/profile': '管理您的个人信息',\n        '/dashboard/files': '管理和下载项目文件',\n        '/dashboard/test-data': '测试系统数据连接',\n        '/dashboard/collaboration/discussion': '与团队成员讨论项目',\n        '/dashboard/collaboration/tasks': '管理项目任务和进度',\n        '/dashboard/task-publish': '为项目团队发布新任务',\n        '/dashboard/task-review': '审核学生提交的任务',\n        // 管理员页面副标题\n        '/dashboard/admin': '查看系统整体运行状态和统计信息',\n        '/dashboard/admin/users': '管理系统中的所有用户账户',\n        '/dashboard/admin/projects': '管理系统中的所有项目',\n        '/dashboard/admin/teams': '管理系统中的所有团队',\n        '/dashboard/admin/settings': '配置系统参数和业务规则',\n        '/dashboard/admin/logs': '查看系统操作日志和错误记录'\n      };\n      return subtitleMap[route.path] || '';\n    };\n    const getBreadcrumb = () => {\n      const breadcrumbMap = {\n        '/dashboard': '首页',\n        '/dashboard/projects': '首页 / 项目列表',\n        '/dashboard/my-projects': '首页 / 我的项目',\n        '/dashboard/projects/create': '首页 / 创建项目',\n        '/dashboard/teams': '首页 / 团队列表',\n        '/dashboard/my-teams': '首页 / 我的团队',\n        '/dashboard/teams/create': '首页 / 创建团队',\n        '/dashboard/review': '首页 / 申请审核',\n        '/dashboard/profile': '首页 / 个人设置',\n        '/dashboard/files': '首页 / 文件管理',\n        '/dashboard/evaluation-center': '首页 / 评价中心',\n        '/dashboard/collaboration/discussion': '首页 / 项目讨论',\n        '/dashboard/collaboration/tasks': '首页 / 任务管理',\n        '/dashboard/task-publish': '首页 / 发布任务',\n        '/dashboard/task-review': '首页 / 任务审核',\n        // 管理员面包屑\n        '/dashboard/admin': '首页 / 系统管理',\n        '/dashboard/admin/users': '首页 / 系统管理 / 用户管理',\n        '/dashboard/admin/projects': '首页 / 系统管理 / 项目管理',\n        '/dashboard/admin/teams': '首页 / 系统管理 / 团队管理',\n        '/dashboard/admin/settings': '首页 / 系统管理 / 系统设置',\n        '/dashboard/admin/logs': '首页 / 系统管理 / 系统日志'\n      };\n      return breadcrumbMap[route.path] || '首页';\n    };\n    const getUserRoleText = () => {\n      if (isAdmin.value) return '管理员';\n      if (isTeacher.value) return '教师';\n      if (isStudent.value) return '学生';\n      return '用户';\n    };\n    const toggleUserDropdown = () => {\n      showUserDropdown.value = !showUserDropdown.value;\n    };\n    const handleCommand = async command => {\n      showUserDropdown.value = false;\n      switch (command) {\n        case 'profile':\n          router.push('/dashboard/profile');\n          break;\n        case 'logout':\n          try {\n            await ElMessageBox.confirm('确定要退出登录吗？', '退出确认', {\n              confirmButtonText: '确定退出',\n              cancelButtonText: '取消',\n              type: 'warning',\n              customClass: 'soft-message-box'\n            });\n            await store.dispatch('logout');\n            ElMessage.success('已安全退出登录');\n            router.push('/auth');\n          } catch (error) {\n            if (error !== 'cancel') {\n              console.error('Logout error:', error);\n            }\n          }\n          break;\n      }\n    };\n\n    // 头像加载成功处理\n    const handleAvatarLoad = event => {\n      console.log('头像加载成功');\n      event.target.style.display = 'block';\n    };\n\n    // 头像加载错误处理\n    const handleAvatarError = event => {\n      console.warn('头像加载失败，使用默认头像');\n      // 隐藏图片元素，显示默认字母头像\n      event.target.style.display = 'none';\n\n      // 清理无效的头像URL，避免重复请求\n      if (store.state.user && store.state.user.avatar) {\n        console.warn('清理无效头像URL:', store.state.user.avatar);\n        // 可以考虑清理store中的无效头像URL\n        // store.commit('SET_USER', { ...store.state.user, avatar: null })\n      }\n    };\n\n    // 点击外部关闭下拉菜单\n    const handleClickOutside = event => {\n      if (!event.target.closest('.user-menu')) {\n        showUserDropdown.value = false;\n      }\n    };\n\n    // 监听点击事件\n    onMounted(() => {\n      document.addEventListener('click', handleClickOutside);\n    });\n\n    // 组件卸载时移除事件监听器\n    onUnmounted(() => {\n      document.removeEventListener('click', handleClickOutside);\n    });\n    return {\n      currentUser,\n      isAdmin,\n      isTeacher,\n      isStudent,\n      showUserDropdown,\n      sidebarCollapsed,\n      // 菜单展开状态\n      projectMenuExpanded,\n      teamMenuExpanded,\n      collaborationMenuExpanded,\n      settingsMenuExpanded,\n      // 方法\n      getPageTitle,\n      getPageSubtitle,\n      getBreadcrumb,\n      getUserRoleText,\n      toggleUserDropdown,\n      toggleSidebar,\n      // 菜单切换方法\n      toggleProjectMenu,\n      toggleTeamMenu,\n      toggleCollaborationMenu,\n      toggleSettingsMenu,\n      handleCommand,\n      // 头像相关方法\n      getAvatarUrl,\n      getInitial,\n      handleAvatarLoad,\n      handleAvatarError\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "ref", "onMounted", "onUnmounted", "useStore", "useRouter", "useRoute", "ElMessage", "ElMessageBox", "House", "Folder", "FolderOpened", "Plus", "DocumentChecked", "UserFilled", "User", "Setting", "ArrowDown", "ArrowRight", "SwitchButton", "ChatDotRound", "ChatLineRound", "List", "Document", "Briefcase", "Star", "Expand", "Fold", "Location", "Monitor", "getAvatarUrl", "getInitial", "AdminNavigation", "name", "components", "setup", "store", "router", "route", "showUserDropdown", "sidebarCollapsed", "projectMenuExpanded", "teamMenuExpanded", "collaborationMenuExpanded", "settingsMenuExpanded", "currentUser", "getters", "isAdmin", "<PERSON><PERSON><PERSON>er", "isStudent", "toggleSidebar", "value", "toggleProjectMenu", "toggleTeamMenu", "toggleCollaborationMenu", "toggleSettingsMenu", "getPageTitle", "routeMap", "path", "getPageSubtitle", "subtitleMap", "getBreadcrumb", "breadcrumbMap", "getUserRoleText", "toggleUserDropdown", "handleCommand", "command", "push", "confirm", "confirmButtonText", "cancelButtonText", "type", "customClass", "dispatch", "success", "error", "console", "handleAvatarLoad", "event", "log", "target", "style", "display", "handleAvatarError", "warn", "state", "user", "avatar", "handleClickOutside", "closest", "document", "addEventListener", "removeEventListener"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\DashboardView.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard dashboard-layout\">\n    <div class=\"dashboard-container\">\n      <!-- 侧边栏 -->\n      <aside class=\"sidebar\" :class=\"{ collapsed: sidebarCollapsed }\">\n        <div class=\"sidebar-content\">\n          <!-- Logo区域 -->\n          <div class=\"logo-section\" v-show=\"!sidebarCollapsed\">\n            <div class=\"logo-content\">\n              <div class=\"logo-icon\">\n                <div class=\"logo-gradient\">\n                  <el-icon size=\"24\"><Briefcase /></el-icon>\n                </div>\n              </div>\n              <h3 class=\"logo-text\">协作平台</h3>\n            </div>\n            <!-- 收起按钮 -->\n            <button class=\"toggle-btn\" @click=\"toggleSidebar\">\n              <el-icon>\n                <transition name=\"icon-fade\" mode=\"out-in\">\n                  <Fold key=\"fold\" />\n                </transition>\n              </el-icon>\n            </button>\n          </div>\n\n          <!-- 收起状态的展开按钮 -->\n          <div class=\"collapsed-toggle\" v-show=\"sidebarCollapsed\">\n            <button class=\"toggle-btn collapsed nav-item-style\" @click=\"toggleSidebar\" title=\"展开导航栏\">\n              <el-icon>\n                <transition name=\"icon-fade\" mode=\"out-in\">\n                  <Expand key=\"expand\" />\n                </transition>\n              </el-icon>\n            </button>\n          </div>\n\n          <!-- 导航菜单 -->\n          <!-- 管理员专用导航 -->\n          <AdminNavigation\n            v-if=\"isAdmin\"\n            :sidebar-collapsed=\"sidebarCollapsed\"\n          />\n\n          <!-- 普通用户导航 -->\n          <nav v-else class=\"nav-menu\">\n            <!-- 仪表板 -->\n            <div class=\"nav-section\">\n              <router-link\n                to=\"/dashboard\"\n                class=\"nav-item\"\n                :class=\"{ active: $route.path === '/dashboard' }\"\n                :title=\"sidebarCollapsed ? '仪表板' : ''\"\n              >\n                <el-icon><House /></el-icon>\n                <transition name=\"fade\">\n                  <span v-show=\"!sidebarCollapsed\">仪表板</span>\n                </transition>\n              </router-link>\n            </div>\n\n            <!-- 项目管理 -->\n            <div class=\"nav-section\">\n              <div\n                v-show=\"!sidebarCollapsed\"\n                class=\"nav-section-title expandable\"\n                @click=\"toggleProjectMenu\"\n                :class=\"{ expanded: projectMenuExpanded }\"\n              >\n                <span>项目管理</span>\n                <el-icon class=\"expand-icon\">\n                  <ArrowRight v-if=\"projectMenuExpanded\" />\n                  <ArrowDown v-else />\n                </el-icon>\n              </div>\n\n              <!-- 项目管理子菜单 -->\n              <div v-show=\"!sidebarCollapsed && projectMenuExpanded\" class=\"nav-submenu-items\">\n                <router-link\n                  v-if=\"isStudent\"\n                  to=\"/dashboard/projects\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/projects' }\"\n                >\n                  <el-icon><Folder /></el-icon>\n                  <span>项目浏览</span>\n                </router-link>\n\n                <!-- 我的项目（教师和学生都有） -->\n                <router-link\n                  v-if=\"isTeacher\"\n                  to=\"/dashboard/my-projects\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/my-projects' }\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                  <span>我的项目</span>\n                </router-link>\n\n                <router-link\n                  v-if=\"isStudent\"\n                  to=\"/dashboard/student-projects\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/student-projects' }\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                  <span>我的项目</span>\n                </router-link>\n\n                <!-- 教师专用功能 -->\n                <template v-if=\"isTeacher\">\n                  <router-link\n                    to=\"/dashboard/projects/create\"\n                    class=\"nav-subitem\"\n                    :class=\"{ active: $route.path === '/dashboard/projects/create' }\"\n                  >\n                    <el-icon><Plus /></el-icon>\n                    <span>创建项目</span>\n                  </router-link>\n                </template>\n\n                <!-- 评价中心（教师和学生都有） -->\n                <router-link\n                  to=\"/dashboard/evaluation-center\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/evaluation-center' }\"\n                >\n                  <el-icon><Star /></el-icon>\n                  <span>评价中心</span>\n                </router-link>\n              </div>\n\n              <!-- 收起状态下的项目管理图标 -->\n              <div v-show=\"sidebarCollapsed\" class=\"collapsed-menu-icons\">\n                <router-link\n                  v-if=\"isStudent\"\n                  to=\"/dashboard/projects\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/projects' }\"\n                  :title=\"'项目浏览'\"\n                >\n                  <el-icon><Folder /></el-icon>\n                </router-link>\n\n                <!-- 我的项目（教师和学生都有） -->\n                <router-link\n                  v-if=\"isTeacher\"\n                  to=\"/dashboard/my-projects\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/my-projects' }\"\n                  :title=\"'我的项目'\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                </router-link>\n\n                <router-link\n                  v-if=\"isStudent\"\n                  to=\"/dashboard/student-projects\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/student-projects' }\"\n                  :title=\"'我的项目'\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                </router-link>\n\n                <!-- 教师专用功能 -->\n                <template v-if=\"isTeacher\">\n                  <router-link\n                    to=\"/dashboard/projects/create\"\n                    class=\"nav-item\"\n                    :class=\"{ active: $route.path === '/dashboard/projects/create' }\"\n                    :title=\"'创建项目'\"\n                  >\n                    <el-icon><Plus /></el-icon>\n                  </router-link>\n                </template>\n\n                <!-- 评价中心（教师和学生都有） -->\n                <router-link\n                  to=\"/dashboard/evaluation-center\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/evaluation-center' }\"\n                  :title=\"'评价中心'\"\n                >\n                  <el-icon><Star /></el-icon>\n                </router-link>\n              </div>\n            </div>\n\n            <!-- 团队管理 -->\n            <div class=\"nav-section\">\n              <div\n                v-show=\"!sidebarCollapsed\"\n                class=\"nav-section-title expandable\"\n                @click=\"toggleTeamMenu\"\n                :class=\"{ expanded: teamMenuExpanded }\"\n              >\n                <span>团队管理</span>\n                <el-icon class=\"expand-icon\">\n                  <ArrowRight v-if=\"teamMenuExpanded\" />\n                  <ArrowDown v-else />\n                </el-icon>\n              </div>\n\n              <!-- 团队管理子菜单 -->\n              <div v-show=\"!sidebarCollapsed && teamMenuExpanded\" class=\"nav-submenu-items\">\n                <!-- 学生团队功能 -->\n                <template v-if=\"isStudent\">\n                  <router-link\n                    to=\"/dashboard/my-teams\"\n                    class=\"nav-subitem\"\n                    :class=\"{ active: $route.path === '/dashboard/my-teams' }\"\n                  >\n                    <el-icon><User /></el-icon>\n                    <span>我的团队</span>\n                  </router-link>\n\n                  <router-link\n                    to=\"/dashboard/teams\"\n                    class=\"nav-subitem\"\n                    :class=\"{ active: $route.path === '/dashboard/teams' }\"\n                  >\n                    <el-icon><UserFilled /></el-icon>\n                    <span>浏览团队</span>\n                  </router-link>\n\n                  <router-link\n                    to=\"/dashboard/teams/create\"\n                    class=\"nav-subitem\"\n                    :class=\"{ active: $route.path === '/dashboard/teams/create' }\"\n                  >\n                    <el-icon><Plus /></el-icon>\n                    <span>创建团队</span>\n                  </router-link>\n                </template>\n\n                <!-- 教师查看团队 -->\n                <template v-if=\"isTeacher\">\n                  <router-link\n                    to=\"/dashboard/teams\"\n                    class=\"nav-subitem\"\n                    :class=\"{ active: $route.path === '/dashboard/teams' }\"\n                  >\n                    <el-icon><UserFilled /></el-icon>\n                    <span>团队管理</span>\n                  </router-link>\n\n                  <router-link\n                    to=\"/dashboard/review\"\n                    class=\"nav-subitem\"\n                    :class=\"{ active: $route.path === '/dashboard/review' }\"\n                  >\n                    <el-icon><DocumentChecked /></el-icon>\n                    <span>申请审核</span>\n                  </router-link>\n                </template>\n              </div>\n\n              <!-- 收起状态下的团队管理图标 -->\n              <div v-show=\"sidebarCollapsed\" class=\"collapsed-menu-icons\">\n                <!-- 学生团队功能 -->\n                <template v-if=\"isStudent\">\n                  <router-link\n                    to=\"/dashboard/my-teams\"\n                    class=\"nav-item\"\n                    :class=\"{ active: $route.path === '/dashboard/my-teams' }\"\n                    :title=\"'我的团队'\"\n                  >\n                    <el-icon><User /></el-icon>\n                  </router-link>\n\n                  <router-link\n                    to=\"/dashboard/teams\"\n                    class=\"nav-item\"\n                    :class=\"{ active: $route.path === '/dashboard/teams' }\"\n                    :title=\"'浏览团队'\"\n                  >\n                    <el-icon><UserFilled /></el-icon>\n                  </router-link>\n\n                  <router-link\n                    to=\"/dashboard/teams/create\"\n                    class=\"nav-item\"\n                    :class=\"{ active: $route.path === '/dashboard/teams/create' }\"\n                    :title=\"'创建团队'\"\n                  >\n                    <el-icon><Plus /></el-icon>\n                  </router-link>\n                </template>\n\n                <!-- 教师查看团队 -->\n                <template v-if=\"isTeacher\">\n                  <router-link\n                    to=\"/dashboard/teams\"\n                    class=\"nav-item\"\n                    :class=\"{ active: $route.path === '/dashboard/teams' }\"\n                    :title=\"'团队管理'\"\n                  >\n                    <el-icon><UserFilled /></el-icon>\n                  </router-link>\n\n                  <router-link\n                    to=\"/dashboard/review\"\n                    class=\"nav-item\"\n                    :class=\"{ active: $route.path === '/dashboard/review' }\"\n                    :title=\"'申请审核'\"\n                  >\n                    <el-icon><DocumentChecked /></el-icon>\n                  </router-link>\n                </template>\n              </div>\n            </div>\n\n            <!-- 协作功能 -->\n            <div class=\"nav-section\">\n              <div\n                v-show=\"!sidebarCollapsed\"\n                class=\"nav-section-title expandable\"\n                @click=\"toggleCollaborationMenu\"\n                :class=\"{ expanded: collaborationMenuExpanded }\"\n              >\n                <span>协作功能</span>\n                <el-icon class=\"expand-icon\">\n                  <ArrowRight v-if=\"collaborationMenuExpanded\" />\n                  <ArrowDown v-else />\n                </el-icon>\n              </div>\n\n              <!-- 协作功能子菜单 -->\n              <div v-show=\"!sidebarCollapsed && collaborationMenuExpanded\" class=\"nav-submenu-items\">\n                <router-link\n                  to=\"/dashboard/collaboration/discussion\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/collaboration/discussion' }\"\n                >\n                  <el-icon><ChatLineRound /></el-icon>\n                  <span>项目讨论</span>\n                </router-link>\n\n                <router-link\n                  to=\"/dashboard/collaboration/tasks\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/collaboration/tasks' }\"\n                >\n                  <el-icon><List /></el-icon>\n                  <span>任务管理</span>\n                </router-link>\n\n                <router-link\n                  to=\"/dashboard/collaboration/space\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path.includes('/dashboard/collaboration/space') }\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                  <span>协作空间</span>\n                </router-link>\n\n                <!-- 教师任务发布 -->\n                <router-link\n                  v-if=\"isTeacher\"\n                  to=\"/dashboard/task-publish\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/task-publish' }\"\n                >\n                  <el-icon><Plus /></el-icon>\n                  <span>发布任务</span>\n                </router-link>\n\n                <!-- 教师任务审核 -->\n                <router-link\n                  v-if=\"isTeacher\"\n                  to=\"/dashboard/task-review\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/task-review' }\"\n                >\n                  <el-icon><DocumentChecked /></el-icon>\n                  <span>任务审核</span>\n                </router-link>\n\n\n\n                <router-link\n                  to=\"/dashboard/file-management\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/file-management' }\"\n                  v-if=\"false\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                  <span>文件管理</span>\n                </router-link>\n\n\n              </div>\n\n              <!-- 收起状态下的协作功能图标 -->\n              <div v-show=\"sidebarCollapsed\" class=\"collapsed-menu-icons\">\n                <router-link\n                  to=\"/dashboard/collaboration/discussion\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/collaboration/discussion' }\"\n                  :title=\"'项目讨论'\"\n                >\n                  <el-icon><ChatLineRound /></el-icon>\n                </router-link>\n\n                <router-link\n                  to=\"/dashboard/collaboration/tasks\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/collaboration/tasks' }\"\n                  :title=\"'任务管理'\"\n                >\n                  <el-icon><List /></el-icon>\n                </router-link>\n\n                <router-link\n                  to=\"/dashboard/collaboration/space\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path.includes('/dashboard/collaboration/space') }\"\n                  :title=\"'协作空间'\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                </router-link>\n\n                <!-- 教师任务发布 -->\n                <router-link\n                  v-if=\"isTeacher\"\n                  to=\"/dashboard/task-publish\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/task-publish' }\"\n                  :title=\"'发布任务'\"\n                >\n                  <el-icon><Plus /></el-icon>\n                </router-link>\n\n                <!-- 教师任务审核 -->\n                <router-link\n                  v-if=\"isTeacher\"\n                  to=\"/dashboard/task-review\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/task-review' }\"\n                  :title=\"'任务审核'\"\n                >\n                  <el-icon><DocumentChecked /></el-icon>\n                </router-link>\n\n\n\n                <router-link\n                  to=\"/dashboard/file-management\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/file-management' }\"\n                  :title=\"'文件管理'\"\n                  v-if=\"false\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                </router-link>\n\n\n              </div>\n            </div>\n\n\n\n\n\n\n\n            <!-- 系统设置 -->\n            <div class=\"nav-section\">\n              <div\n                v-show=\"!sidebarCollapsed\"\n                class=\"nav-section-title expandable\"\n                @click=\"toggleSettingsMenu\"\n                :class=\"{ expanded: settingsMenuExpanded }\"\n              >\n                <span>系统设置</span>\n                <el-icon class=\"expand-icon\">\n                  <ArrowRight v-if=\"settingsMenuExpanded\" />\n                  <ArrowDown v-else />\n                </el-icon>\n              </div>\n\n              <!-- 系统设置子菜单 -->\n              <div v-show=\"!sidebarCollapsed && settingsMenuExpanded\" class=\"nav-submenu-items\">\n                <router-link\n                  to=\"/dashboard/profile\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/profile' }\"\n                >\n                  <el-icon><Setting /></el-icon>\n                  <span>个人设置</span>\n                </router-link>\n              </div>\n\n              <!-- 收起状态下的系统设置图标 -->\n              <div v-show=\"sidebarCollapsed\" class=\"collapsed-menu-icons\">\n                <router-link\n                  to=\"/dashboard/profile\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/profile' }\"\n                  :title=\"'个人设置'\"\n                >\n                  <el-icon><Setting /></el-icon>\n                </router-link>\n              </div>\n            </div>\n          </nav>\n        </div>\n      </aside>\n\n      <!-- 主内容区 -->\n      <main class=\"main-content\">\n        <!-- 顶部导航栏 -->\n        <header class=\"top-header\">\n          <div class=\"header-content\">\n            <div class=\"header-left\">\n              <div class=\"breadcrumb\">\n                <el-icon class=\"breadcrumb-icon\"><Location /></el-icon>\n                <span class=\"breadcrumb-text\">{{ getBreadcrumb() }}</span>\n              </div>\n              <h1 class=\"page-title\">{{ getPageTitle() }}</h1>\n              <p class=\"page-subtitle\">{{ getPageSubtitle() }}</p>\n            </div>\n\n            <div class=\"header-right\">\n              <!-- 通知中心 -->\n              <!-- <NotificationCenter /> -->\n\n              <!-- 用户下拉菜单 -->\n              <div class=\"user-menu\" @click=\"toggleUserDropdown\">\n                <div class=\"user-info\">\n                  <div class=\"avatar\">\n                    <img v-if=\"getAvatarUrl(currentUser?.avatar)\" :src=\"getAvatarUrl(currentUser?.avatar)\" :alt=\"currentUser?.realName\" @error=\"handleAvatarError\" @load=\"handleAvatarLoad\" />\n                    <span v-else>{{ getInitial(currentUser?.realName) }}</span>\n                  </div>\n                  <div class=\"user-details\">\n                    <span class=\"user-name\">{{ currentUser?.realName }}</span>\n                    <span class=\"user-role\">{{ getUserRoleText() }}</span>\n                  </div>\n                  <el-icon class=\"dropdown-arrow\" :class=\"{ rotated: showUserDropdown }\">\n                    <ArrowDown />\n                  </el-icon>\n                </div>\n\n                <!-- 下拉菜单 -->\n                <div class=\"dropdown-content\" :class=\"{ active: showUserDropdown }\">\n                  <div class=\"dropdown-item\" @click.prevent=\"handleCommand('profile')\">\n                    <el-icon><User /></el-icon>\n                    <span>个人资料</span>\n                  </div>\n                  <div class=\"dropdown-divider\"></div>\n                  <div class=\"dropdown-item logout\" @click.prevent=\"handleCommand('logout')\">\n                    <el-icon><SwitchButton /></el-icon>\n                    <span>退出登录</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        <!-- 页面内容 -->\n        <div class=\"page-content\">\n          <router-view />\n        </div>\n      </main>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed, ref, onMounted, onUnmounted } from 'vue'\nimport { useStore } from 'vuex'\nimport { useRouter, useRoute } from 'vue-router'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport {\n  House, Folder, FolderOpened, Plus, DocumentChecked,\n  UserFilled, User, Setting, ArrowDown, ArrowRight, SwitchButton,\n  ChatDotRound, ChatLineRound, List, Document,\n  Briefcase, Star, Expand, Fold, Location, Monitor\n} from '@element-plus/icons-vue'\nimport { getAvatarUrl, getInitial } from '@/utils/avatar'\nimport AdminNavigation from '@/components/AdminNavigation.vue'\n// import NotificationCenter from '@/components/NotificationCenter.vue'\n\nexport default {\n  name: 'DashboardView',\n  components: {\n    AdminNavigation,\n    House,\n    Folder,\n    FolderOpened,\n    Plus,\n    DocumentChecked,\n    UserFilled,\n    User,\n    Setting,\n    ArrowDown,\n    ArrowRight,\n    SwitchButton,\n    ChatDotRound,\n    ChatLineRound,\n    List,\n    Document,\n    Briefcase,\n    Star,\n    Expand,\n    Fold,\n    Location,\n    Monitor\n    // NotificationCenter\n  },\n  setup() {\n    const store = useStore()\n    const router = useRouter()\n    const route = useRoute()\n\n    const showUserDropdown = ref(false)\n    const sidebarCollapsed = ref(false)\n\n    // 各个菜单的展开状态，默认都展开\n    const projectMenuExpanded = ref(true)\n    const teamMenuExpanded = ref(true)\n    const collaborationMenuExpanded = ref(true)\n    const settingsMenuExpanded = ref(true)\n\n    const currentUser = computed(() => store.getters.currentUser)\n    const isAdmin = computed(() => store.getters.isAdmin)\n    const isTeacher = computed(() => store.getters.isTeacher)\n    const isStudent = computed(() => store.getters.isStudent)\n\n    const toggleSidebar = () => {\n      sidebarCollapsed.value = !sidebarCollapsed.value\n    }\n\n    // 各个菜单的切换方法\n    const toggleProjectMenu = () => {\n      projectMenuExpanded.value = !projectMenuExpanded.value\n    }\n\n    const toggleTeamMenu = () => {\n      teamMenuExpanded.value = !teamMenuExpanded.value\n    }\n\n    const toggleCollaborationMenu = () => {\n      collaborationMenuExpanded.value = !collaborationMenuExpanded.value\n    }\n\n    const toggleSettingsMenu = () => {\n      settingsMenuExpanded.value = !settingsMenuExpanded.value\n    }\n\n    const getPageTitle = () => {\n      const routeMap = {\n        '/dashboard': '仪表板',\n        '/dashboard/projects': '项目列表',\n        '/dashboard/my-projects': '我的项目',\n        '/dashboard/projects/create': '创建项目',\n        '/dashboard/teams': '团队列表',\n        '/dashboard/my-teams': '我的团队',\n        '/dashboard/teams/create': '创建团队',\n        '/dashboard/review': '申请审核',\n        '/dashboard/profile': '个人设置',\n\n        '/dashboard/files': '文件管理',\n        '/dashboard/evaluation-center': '评价中心',\n        '/dashboard/collaboration/discussion': '项目讨论',\n        '/dashboard/collaboration/tasks': '任务管理',\n        '/dashboard/collaboration/space': '协作空间',\n        '/dashboard/task-publish': '发布任务',\n        '/dashboard/task-review': '任务审核',\n\n        // 管理员页面\n        '/dashboard/admin': '管理仪表板',\n        '/dashboard/admin/users': '用户管理',\n        '/dashboard/admin/projects': '项目管理',\n        '/dashboard/admin/teams': '团队管理',\n        '/dashboard/admin/settings': '系统设置',\n        '/dashboard/admin/logs': '系统日志'\n      }\n      return routeMap[route.path] || '项目协作平台'\n    }\n\n    const getPageSubtitle = () => {\n      const subtitleMap = {\n        '/dashboard': '欢迎回来，查看您的项目概览',\n        '/dashboard/projects': '浏览所有可用的项目',\n        '/dashboard/my-projects': '管理您创建的项目',\n        '/dashboard/projects/create': '创建新的协作项目',\n        '/dashboard/teams': '查看所有团队信息',\n        '/dashboard/my-teams': '管理您的团队',\n        '/dashboard/teams/create': '组建新的项目团队',\n        '/dashboard/review': '审核学生的申请',\n        '/dashboard/profile': '管理您的个人信息',\n\n        '/dashboard/files': '管理和下载项目文件',\n        '/dashboard/test-data': '测试系统数据连接',\n        '/dashboard/collaboration/discussion': '与团队成员讨论项目',\n        '/dashboard/collaboration/tasks': '管理项目任务和进度',\n        '/dashboard/task-publish': '为项目团队发布新任务',\n        '/dashboard/task-review': '审核学生提交的任务',\n\n        // 管理员页面副标题\n        '/dashboard/admin': '查看系统整体运行状态和统计信息',\n        '/dashboard/admin/users': '管理系统中的所有用户账户',\n        '/dashboard/admin/projects': '管理系统中的所有项目',\n        '/dashboard/admin/teams': '管理系统中的所有团队',\n        '/dashboard/admin/settings': '配置系统参数和业务规则',\n        '/dashboard/admin/logs': '查看系统操作日志和错误记录'\n      }\n      return subtitleMap[route.path] || ''\n    }\n\n    const getBreadcrumb = () => {\n      const breadcrumbMap = {\n        '/dashboard': '首页',\n        '/dashboard/projects': '首页 / 项目列表',\n        '/dashboard/my-projects': '首页 / 我的项目',\n        '/dashboard/projects/create': '首页 / 创建项目',\n        '/dashboard/teams': '首页 / 团队列表',\n        '/dashboard/my-teams': '首页 / 我的团队',\n        '/dashboard/teams/create': '首页 / 创建团队',\n        '/dashboard/review': '首页 / 申请审核',\n        '/dashboard/profile': '首页 / 个人设置',\n\n        '/dashboard/files': '首页 / 文件管理',\n        '/dashboard/evaluation-center': '首页 / 评价中心',\n        '/dashboard/collaboration/discussion': '首页 / 项目讨论',\n        '/dashboard/collaboration/tasks': '首页 / 任务管理',\n        '/dashboard/task-publish': '首页 / 发布任务',\n        '/dashboard/task-review': '首页 / 任务审核',\n\n        // 管理员面包屑\n        '/dashboard/admin': '首页 / 系统管理',\n        '/dashboard/admin/users': '首页 / 系统管理 / 用户管理',\n        '/dashboard/admin/projects': '首页 / 系统管理 / 项目管理',\n        '/dashboard/admin/teams': '首页 / 系统管理 / 团队管理',\n        '/dashboard/admin/settings': '首页 / 系统管理 / 系统设置',\n        '/dashboard/admin/logs': '首页 / 系统管理 / 系统日志'\n      }\n      return breadcrumbMap[route.path] || '首页'\n    }\n\n    const getUserRoleText = () => {\n      if (isAdmin.value) return '管理员'\n      if (isTeacher.value) return '教师'\n      if (isStudent.value) return '学生'\n      return '用户'\n    }\n\n    const toggleUserDropdown = () => {\n      showUserDropdown.value = !showUserDropdown.value\n    }\n\n\n\n    const handleCommand = async (command) => {\n      showUserDropdown.value = false\n\n      switch (command) {\n        case 'profile':\n          router.push('/dashboard/profile')\n          break\n        case 'logout':\n          try {\n            await ElMessageBox.confirm(\n              '确定要退出登录吗？',\n              '退出确认',\n              {\n                confirmButtonText: '确定退出',\n                cancelButtonText: '取消',\n                type: 'warning',\n                customClass: 'soft-message-box'\n              }\n            )\n\n            await store.dispatch('logout')\n            ElMessage.success('已安全退出登录')\n            router.push('/auth')\n          } catch (error) {\n            if (error !== 'cancel') {\n              console.error('Logout error:', error)\n            }\n          }\n          break\n      }\n    }\n\n    // 头像加载成功处理\n    const handleAvatarLoad = (event) => {\n      console.log('头像加载成功')\n      event.target.style.display = 'block'\n    }\n\n    // 头像加载错误处理\n    const handleAvatarError = (event) => {\n      console.warn('头像加载失败，使用默认头像')\n      // 隐藏图片元素，显示默认字母头像\n      event.target.style.display = 'none'\n\n      // 清理无效的头像URL，避免重复请求\n      if (store.state.user && store.state.user.avatar) {\n        console.warn('清理无效头像URL:', store.state.user.avatar)\n        // 可以考虑清理store中的无效头像URL\n        // store.commit('SET_USER', { ...store.state.user, avatar: null })\n      }\n    }\n\n    // 点击外部关闭下拉菜单\n    const handleClickOutside = (event) => {\n      if (!event.target.closest('.user-menu')) {\n        showUserDropdown.value = false\n      }\n    }\n\n    // 监听点击事件\n    onMounted(() => {\n      document.addEventListener('click', handleClickOutside)\n    })\n\n    // 组件卸载时移除事件监听器\n    onUnmounted(() => {\n      document.removeEventListener('click', handleClickOutside)\n    })\n\n    return {\n      currentUser,\n      isAdmin,\n      isTeacher,\n      isStudent,\n      showUserDropdown,\n      sidebarCollapsed,\n      // 菜单展开状态\n      projectMenuExpanded,\n      teamMenuExpanded,\n      collaborationMenuExpanded,\n      settingsMenuExpanded,\n      // 方法\n      getPageTitle,\n      getPageSubtitle,\n      getBreadcrumb,\n      getUserRoleText,\n      toggleUserDropdown,\n      toggleSidebar,\n      // 菜单切换方法\n      toggleProjectMenu,\n      toggleTeamMenu,\n      toggleCollaborationMenu,\n      toggleSettingsMenu,\n      handleCommand,\n      // 头像相关方法\n      getAvatarUrl,\n      getInitial,\n      handleAvatarLoad,\n      handleAvatarError\n    }\n  }\n}\n</script>\n\n<style scoped>\n.dashboard {\n  height: 100vh;\n  background: var(--background-color);\n}\n\n.dashboard-container {\n  display: flex;\n  height: 100vh;\n}\n\n/* 侧边栏样式 */\n.sidebar {\n  width: 280px;\n  background: #ffffff;\n  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);\n  border-radius: 0;\n  overflow: hidden;\n  position: relative;\n  z-index: 10;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  border-right: 1px solid rgba(0, 0, 0, 0.08);\n}\n\n.sidebar.collapsed {\n  width: 80px;\n}\n\n.sidebar-content {\n  height: 100%;\n  padding: var(--space-6);\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n\n/* Logo区域 */\n.logo-section {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: var(--space-8);\n  padding: var(--space-6) var(--space-5);\n  background: transparent;\n  border-radius: 0;\n  border: none;\n  position: relative;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.06);\n}\n\n.sidebar.collapsed .logo-section {\n  justify-content: center;\n  padding: var(--space-3);\n}\n\n.logo-content {\n  display: flex;\n  align-items: center;\n  gap: var(--space-4);\n}\n\n.sidebar.collapsed .logo-content {\n  justify-content: center;\n}\n\n.logo-icon {\n  width: 48px;\n  height: 48px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 12px;\n  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);\n  color: white;\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);\n  transition: all 0.3s ease;\n}\n\n.logo-icon:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);\n}\n\n.logo-text {\n  font-size: var(--font-size-xl);\n  font-weight: var(--font-weight-bold);\n  color: var(--text-primary);\n  margin: 0;\n  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.logo-icon-only {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 10px;\n  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);\n  color: white;\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);\n  transition: all 0.3s ease;\n  margin: 0 auto;\n}\n\n.logo-icon-only:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);\n}\n\n/* 侧边栏切换按钮 */\n.toggle-btn {\n  width: 32px;\n  height: 32px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.8);\n  color: #6366f1;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n}\n\n.toggle-btn .el-icon {\n  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.toggle-btn:active {\n  transform: scale(0.95);\n}\n\n.toggle-btn:active .el-icon {\n  transform: scale(0.9);\n}\n\n.collapsed-toggle {\n  padding: 0;\n  margin: 0 4px 16px 4px;\n  display: flex;\n  justify-content: center;\n}\n\n.toggle-btn.collapsed.nav-item-style {\n  width: 100%;\n  height: auto;\n  background: transparent;\n  border-radius: 12px;\n  box-shadow: none;\n  padding: 12px;\n  justify-content: center;\n  align-items: center;\n  color: #64748b;\n  font-weight: 500;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.toggle-btn.collapsed.nav-item-style .el-icon {\n  font-size: 16px;\n}\n\n.toggle-btn:hover {\n  background: #6366f1;\n  color: white;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);\n}\n\n.toggle-btn:hover .el-icon {\n  transform: scale(1.1);\n}\n\n.sidebar.collapsed .toggle-btn.nav-item-style:hover {\n  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);\n  color: #6366f1;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);\n}\n\n.sidebar.collapsed .toggle-btn.nav-item-style:active {\n  transform: scale(0.95);\n}\n\n/* 导航菜单 */\n.nav-menu {\n  display: flex;\n  flex-direction: column;\n  gap: var(--space-5);\n}\n\n.nav-section {\n  display: flex;\n  flex-direction: column;\n  gap: var(--space-2);\n}\n\n.nav-section-title {\n  font-size: 11px;\n  font-weight: 600;\n  color: #64748b;\n  text-transform: uppercase;\n  letter-spacing: 0.8px;\n  margin-bottom: var(--space-3);\n  padding: 0 var(--space-4);\n  transition: opacity 0.3s ease;\n}\n\n.nav-section-title.expandable {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  cursor: pointer;\n  padding: var(--space-4) var(--space-5);\n  border-radius: 0;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  background: transparent;\n  border: none;\n  margin-bottom: var(--space-1);\n  position: relative;\n  font-weight: var(--font-weight-semibold);\n  font-size: var(--text-sm);\n  color: var(--gray-700);\n  letter-spacing: 0.025em;\n}\n\n.nav-section-title.expandable::after {\n  content: '';\n  position: absolute;\n  left: var(--space-5);\n  right: var(--space-5);\n  bottom: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, var(--gray-300), transparent);\n  opacity: 0.6;\n}\n\n.nav-section-title.expandable:hover {\n  color: var(--gray-900);\n  background: rgba(0, 0, 0, 0.02);\n}\n\n.nav-section-title.expandable.expanded {\n  color: var(--primary-600);\n  background: rgba(99, 102, 241, 0.04);\n}\n\n.nav-section-title.expandable.expanded::after {\n  background: linear-gradient(90deg, transparent, var(--primary-300), transparent);\n}\n\n.expand-icon {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  font-size: 14px;\n  color: var(--gray-500);\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: var(--radius-full);\n  background: transparent;\n}\n\n.nav-section-title.expandable:hover .expand-icon {\n  color: var(--gray-700);\n  background: rgba(0, 0, 0, 0.05);\n}\n\n.nav-section-title.expandable.expanded .expand-icon {\n  transform: rotate(90deg);\n  color: var(--primary-600);\n  background: rgba(99, 102, 241, 0.1);\n}\n\n.nav-submenu-items {\n  margin-left: 0;\n  border-left: none;\n  padding-left: 0;\n  margin-top: var(--space-2);\n  position: relative;\n  background: transparent;\n  border-radius: 0;\n  padding-top: 0;\n  padding-bottom: var(--space-3);\n}\n\n.nav-subitem {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-2_5) var(--space-5);\n  color: var(--gray-500);\n  text-decoration: none;\n  border-radius: 0;\n  font-size: var(--text-sm);\n  font-weight: var(--font-weight-regular);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  margin-bottom: 0;\n  border: none;\n  position: relative;\n  background: transparent;\n  padding-left: calc(var(--space-5) + var(--space-4));\n}\n\n.nav-subitem::before {\n  content: '';\n  position: absolute;\n  left: calc(var(--space-5) + var(--space-2));\n  top: 50%;\n  transform: translateY(-50%);\n  width: 4px;\n  height: 4px;\n  border-radius: var(--radius-full);\n  background: var(--gray-400);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.nav-subitem:hover {\n  background: rgba(0, 0, 0, 0.02);\n  color: var(--gray-700);\n  padding-left: calc(var(--space-5) + var(--space-5));\n}\n\n.nav-subitem:hover::before {\n  background: var(--primary-500);\n  transform: translateY(-50%) scale(1.5);\n}\n\n.nav-subitem.active {\n  background: rgba(99, 102, 241, 0.06);\n  color: var(--primary-700);\n  font-weight: var(--font-weight-medium);\n}\n\n.nav-subitem.active::before {\n  background: var(--primary-500);\n  transform: translateY(-50%) scale(1.5);\n}\n\n.collapsed-menu-icons {\n  display: flex;\n  flex-direction: column;\n  gap: var(--space-1);\n}\n\n.nav-item {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-3) var(--space-5);\n  border-radius: 0;\n  color: var(--gray-600);\n  text-decoration: none;\n  font-weight: var(--font-weight-medium);\n  font-size: var(--text-sm);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  margin: 0;\n  border: none;\n  background: transparent;\n}\n\n.sidebar.collapsed .nav-item {\n  justify-content: center;\n  padding: 12px;\n  margin: 0 8px;\n}\n\n.nav-item::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  width: 3px;\n  background: var(--primary-500);\n  transform: scaleY(0);\n  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  border-radius: 0 2px 2px 0;\n}\n\n.nav-item:hover {\n  background: rgba(0, 0, 0, 0.03);\n  color: var(--gray-900);\n  padding-left: calc(var(--space-5) + var(--space-1));\n}\n\n.nav-item.active {\n  background: rgba(99, 102, 241, 0.08);\n  color: var(--primary-700);\n  font-weight: var(--font-weight-semibold);\n}\n\n.nav-item.active::before {\n  transform: scaleY(1);\n}\n\n.nav-item.active:hover {\n  background: rgba(99, 102, 241, 0.12);\n}\n\n.nav-item.active::before {\n  content: '';\n  position: absolute;\n  left: -6px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 3px;\n  height: 24px;\n  background: var(--primary-gradient);\n  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;\n  box-shadow: var(--shadow-sm);\n}\n\n.sidebar.collapsed .nav-item.active::before {\n  display: none;\n}\n\n/* 过渡动画 */\n.fade-enter-active, .fade-leave-active {\n  transition: opacity 0.3s ease;\n}\n\n.fade-enter-from, .fade-leave-to {\n  opacity: 0;\n}\n\n/* 图标切换动画 */\n.icon-fade-enter-active, .icon-fade-leave-active {\n  transition: all 0.2s ease;\n}\n\n.icon-fade-enter-from {\n  opacity: 0;\n  transform: scale(0.8) rotate(90deg);\n}\n\n.icon-fade-leave-to {\n  opacity: 0;\n  transform: scale(0.8) rotate(-90deg);\n}\n\n/* 子菜单 */\n.nav-submenu {\n  margin-left: var(--space-4);\n}\n\n.nav-submenu-title {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-3) var(--space-4);\n  color: var(--text-secondary);\n  font-weight: var(--font-weight-medium);\n  font-size: var(--font-size-sm);\n}\n\n.nav-submenu-items {\n  display: flex;\n  flex-direction: column;\n  gap: var(--space-1);\n  margin-left: var(--space-6);\n  padding-left: var(--space-4);\n  border-left: 2px solid rgba(160, 118, 249, 0.2);\n}\n\n.nav-subitem {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-3);\n  border-radius: var(--radius-md);\n  color: var(--text-secondary);\n  text-decoration: none;\n  font-size: var(--font-size-sm);\n  transition: all var(--transition-base);\n}\n\n.nav-subitem:hover {\n  background: rgba(160, 118, 249, 0.1);\n  color: var(--primary-gradient-start);\n}\n\n.nav-subitem.active {\n  background: var(--primary-gradient);\n  color: var(--text-on-primary);\n}\n\n/* 主内容区域 */\n.main-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* 顶部导航栏 */\n.top-header {\n  background: #ffffff;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border-radius: 0;\n  margin: 0;\n  position: relative;\n  z-index: 5;\n  border: none;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.06);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: var(--space-5) 0;\n  width: 100%;\n  min-height: 80px;\n}\n\n.header-left {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding-left: var(--space-6);\n}\n\n.header-right {\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n  padding-right: var(--space-6);\n}\n\n/* 面包屑导航 */\n.breadcrumb {\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin-bottom: var(--space-2);\n  padding: 0;\n  background: transparent;\n  border: none;\n  width: fit-content;\n}\n\n.breadcrumb-icon {\n  color: var(--gray-400);\n  font-size: 14px;\n  margin-right: var(--space-1);\n}\n\n.breadcrumb-text {\n  font-size: var(--text-xs);\n  color: var(--gray-500);\n  font-weight: var(--font-weight-medium);\n  font-family: var(--font-sans);\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.page-title {\n  font-size: 24px;\n  font-weight: 600;\n  color: var(--gray-900);\n  margin: 0 0 var(--space-1) 0;\n  background: none;\n  line-height: 1.3;\n  font-family: var(--font-sans);\n  position: relative;\n  letter-spacing: -0.01em;\n}\n\n.page-subtitle {\n  font-size: var(--text-sm);\n  color: var(--gray-500);\n  margin: 0;\n  font-weight: var(--font-weight-regular);\n  font-family: var(--font-sans);\n  line-height: var(--leading-relaxed);\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: var(--space-4);\n}\n\n/* 通知按钮 */\n.notification-btn {\n  position: relative;\n  width: 44px;\n  height: 44px;\n  border: none;\n  border-radius: var(--radius-full);\n  background: var(--background-color);\n  color: var(--text-secondary);\n  cursor: pointer;\n  transition: all var(--transition-base);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.notification-btn:hover {\n  background: var(--primary-gradient);\n  color: var(--text-on-primary);\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-soft-colored);\n}\n\n.notification-badge {\n  position: absolute;\n  top: -2px;\n  right: -2px;\n  width: 18px;\n  height: 18px;\n  background: var(--accent-color);\n  color: white;\n  border-radius: 50%;\n  font-size: 10px;\n  font-weight: var(--font-weight-bold);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid var(--surface-color);\n}\n\n/* 用户菜单 */\n.user-menu {\n  position: relative;\n  cursor: pointer;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  background: transparent;\n  border: 1px solid rgba(0, 0, 0, 0.08);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: none;\n}\n\n.user-info:hover {\n  background: rgba(0, 0, 0, 0.02);\n  border-color: rgba(0, 0, 0, 0.12);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n}\n\n.avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: var(--radius-lg);\n  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));\n  color: var(--text-on-primary);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: var(--font-weight-semibold);\n  font-size: var(--text-sm);\n  border: none;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);\n  overflow: hidden;\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n}\n\n.avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.user-details {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  margin-right: var(--space-2);\n}\n\n.user-name {\n  font-size: var(--text-sm);\n  font-weight: var(--font-weight-semibold);\n  color: var(--gray-900);\n  line-height: var(--leading-tight);\n  font-family: var(--font-sans);\n  margin-bottom: 2px;\n}\n\n.user-role {\n  font-size: var(--text-xs);\n  color: var(--gray-500);\n  line-height: var(--leading-tight);\n  font-family: var(--font-sans);\n  font-weight: var(--font-weight-regular);\n}\n\n.dropdown-arrow {\n  color: var(--gray-400);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  font-size: 14px;\n}\n\n.user-info:hover .dropdown-arrow {\n  color: var(--gray-600);\n}\n\n.dropdown-arrow.rotated {\n  transform: rotate(180deg);\n}\n\n/* 下拉菜单 */\n.dropdown-content {\n  position: absolute;\n  top: calc(100% + 8px);\n  right: 0;\n  min-width: 200px;\n  background: #ffffff;\n  border-radius: var(--radius-xl);\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  padding: var(--space-2);\n  z-index: var(--z-dropdown);\n  opacity: 0;\n  visibility: hidden;\n  transform: translateY(-8px);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  border: 1px solid rgba(0, 0, 0, 0.08);\n}\n\n.dropdown-content.active {\n  opacity: 1;\n  visibility: visible;\n  transform: translateY(0);\n}\n\n.dropdown-item {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-3);\n  border-radius: var(--radius-lg);\n  color: var(--gray-700);\n  text-decoration: none;\n  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  font-size: var(--text-sm);\n  font-family: var(--font-sans);\n  font-weight: var(--font-weight-medium);\n  border: none;\n  position: relative;\n  background: transparent;\n}\n\n.dropdown-item:hover {\n  background: rgba(0, 0, 0, 0.04);\n  color: var(--gray-900);\n}\n\n.dropdown-item .el-icon {\n  font-size: 16px;\n  color: var(--gray-500);\n  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.dropdown-item:hover .el-icon {\n  color: var(--gray-700);\n}\n\n.dropdown-item.logout {\n  color: var(--error-600);\n}\n\n.dropdown-item.logout:hover {\n  background: rgba(239, 68, 68, 0.08);\n  color: var(--error-700);\n}\n\n.dropdown-item.logout .el-icon {\n  color: var(--error-500);\n}\n\n.dropdown-item.logout:hover .el-icon {\n  color: var(--error-600);\n}\n\n.dropdown-divider {\n  height: 1px;\n  background: rgba(0, 0, 0, 0.08);\n  margin: var(--space-1) 0;\n}\n\n/* 页面内容 */\n.page-content {\n  flex: 1;\n  padding: var(--space-6) var(--space-4) var(--space-4) var(--space-4);\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .sidebar {\n    width: 240px;\n  }\n\n  .header-content {\n    padding: var(--space-4) var(--space-6);\n  }\n\n  .page-title {\n    font-size: 20px;\n  }\n\n  .user-details {\n    display: none;\n  }\n\n  .page-content {\n    padding: var(--space-4);\n  }\n}\n\n@media (max-width: 640px) {\n  .dashboard-container {\n    flex-direction: column;\n  }\n\n  .sidebar {\n    width: 100%;\n    height: auto;\n    border-radius: 0;\n  }\n\n  .sidebar-content {\n    padding: var(--space-4);\n  }\n\n  .nav-menu {\n    flex-direction: row;\n    overflow-x: auto;\n    gap: var(--space-2);\n  }\n\n  .nav-section {\n    flex-direction: row;\n    min-width: max-content;\n  }\n\n  .nav-section-title {\n    display: none;\n  }\n}\n</style>\n"], "mappings": ";AA2jBA,SAASA,QAAQ,EAAEC,GAAG,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AAC1D,SAASC,QAAO,QAAS,MAAK;AAC9B,SAASC,SAAS,EAAEC,QAAO,QAAS,YAAW;AAC/C,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SACEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,IAAI,EAAEC,eAAe,EAClDC,UAAU,EAAEC,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEC,UAAU,EAAEC,YAAY,EAC9DC,YAAY,EAAEC,aAAa,EAAEC,IAAI,EAAEC,QAAQ,EAC3CC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAM,QAC1C,yBAAwB;AAC/B,SAASC,YAAY,EAAEC,UAAS,QAAS,gBAAe;AACxD,OAAOC,eAAc,MAAO,kCAAiC;AAC7D;;AAEA,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVF,eAAe;IACfvB,KAAK;IACLC,MAAM;IACNC,YAAY;IACZC,IAAI;IACJC,eAAe;IACfC,UAAU;IACVC,IAAI;IACJC,OAAO;IACPC,SAAS;IACTC,UAAU;IACVC,YAAY;IACZC,YAAY;IACZC,aAAa;IACbC,IAAI;IACJC,QAAQ;IACRC,SAAS;IACTC,IAAI;IACJC,MAAM;IACNC,IAAI;IACJC,QAAQ;IACRC;IACA;EACF,CAAC;EACDM,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIhC,QAAQ,CAAC;IACvB,MAAMiC,MAAK,GAAIhC,SAAS,CAAC;IACzB,MAAMiC,KAAI,GAAIhC,QAAQ,CAAC;IAEvB,MAAMiC,gBAAe,GAAItC,GAAG,CAAC,KAAK;IAClC,MAAMuC,gBAAe,GAAIvC,GAAG,CAAC,KAAK;;IAElC;IACA,MAAMwC,mBAAkB,GAAIxC,GAAG,CAAC,IAAI;IACpC,MAAMyC,gBAAe,GAAIzC,GAAG,CAAC,IAAI;IACjC,MAAM0C,yBAAwB,GAAI1C,GAAG,CAAC,IAAI;IAC1C,MAAM2C,oBAAmB,GAAI3C,GAAG,CAAC,IAAI;IAErC,MAAM4C,WAAU,GAAI7C,QAAQ,CAAC,MAAMoC,KAAK,CAACU,OAAO,CAACD,WAAW;IAC5D,MAAME,OAAM,GAAI/C,QAAQ,CAAC,MAAMoC,KAAK,CAACU,OAAO,CAACC,OAAO;IACpD,MAAMC,SAAQ,GAAIhD,QAAQ,CAAC,MAAMoC,KAAK,CAACU,OAAO,CAACE,SAAS;IACxD,MAAMC,SAAQ,GAAIjD,QAAQ,CAAC,MAAMoC,KAAK,CAACU,OAAO,CAACG,SAAS;IAExD,MAAMC,aAAY,GAAIA,CAAA,KAAM;MAC1BV,gBAAgB,CAACW,KAAI,GAAI,CAACX,gBAAgB,CAACW,KAAI;IACjD;;IAEA;IACA,MAAMC,iBAAgB,GAAIA,CAAA,KAAM;MAC9BX,mBAAmB,CAACU,KAAI,GAAI,CAACV,mBAAmB,CAACU,KAAI;IACvD;IAEA,MAAME,cAAa,GAAIA,CAAA,KAAM;MAC3BX,gBAAgB,CAACS,KAAI,GAAI,CAACT,gBAAgB,CAACS,KAAI;IACjD;IAEA,MAAMG,uBAAsB,GAAIA,CAAA,KAAM;MACpCX,yBAAyB,CAACQ,KAAI,GAAI,CAACR,yBAAyB,CAACQ,KAAI;IACnE;IAEA,MAAMI,kBAAiB,GAAIA,CAAA,KAAM;MAC/BX,oBAAoB,CAACO,KAAI,GAAI,CAACP,oBAAoB,CAACO,KAAI;IACzD;IAEA,MAAMK,YAAW,GAAIA,CAAA,KAAM;MACzB,MAAMC,QAAO,GAAI;QACf,YAAY,EAAE,KAAK;QACnB,qBAAqB,EAAE,MAAM;QAC7B,wBAAwB,EAAE,MAAM;QAChC,4BAA4B,EAAE,MAAM;QACpC,kBAAkB,EAAE,MAAM;QAC1B,qBAAqB,EAAE,MAAM;QAC7B,yBAAyB,EAAE,MAAM;QACjC,mBAAmB,EAAE,MAAM;QAC3B,oBAAoB,EAAE,MAAM;QAE5B,kBAAkB,EAAE,MAAM;QAC1B,8BAA8B,EAAE,MAAM;QACtC,qCAAqC,EAAE,MAAM;QAC7C,gCAAgC,EAAE,MAAM;QACxC,gCAAgC,EAAE,MAAM;QACxC,yBAAyB,EAAE,MAAM;QACjC,wBAAwB,EAAE,MAAM;QAEhC;QACA,kBAAkB,EAAE,OAAO;QAC3B,wBAAwB,EAAE,MAAM;QAChC,2BAA2B,EAAE,MAAM;QACnC,wBAAwB,EAAE,MAAM;QAChC,2BAA2B,EAAE,MAAM;QACnC,uBAAuB,EAAE;MAC3B;MACA,OAAOA,QAAQ,CAACnB,KAAK,CAACoB,IAAI,KAAK,QAAO;IACxC;IAEA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5B,MAAMC,WAAU,GAAI;QAClB,YAAY,EAAE,eAAe;QAC7B,qBAAqB,EAAE,WAAW;QAClC,wBAAwB,EAAE,UAAU;QACpC,4BAA4B,EAAE,UAAU;QACxC,kBAAkB,EAAE,UAAU;QAC9B,qBAAqB,EAAE,QAAQ;QAC/B,yBAAyB,EAAE,UAAU;QACrC,mBAAmB,EAAE,SAAS;QAC9B,oBAAoB,EAAE,UAAU;QAEhC,kBAAkB,EAAE,WAAW;QAC/B,sBAAsB,EAAE,UAAU;QAClC,qCAAqC,EAAE,WAAW;QAClD,gCAAgC,EAAE,WAAW;QAC7C,yBAAyB,EAAE,YAAY;QACvC,wBAAwB,EAAE,WAAW;QAErC;QACA,kBAAkB,EAAE,iBAAiB;QACrC,wBAAwB,EAAE,cAAc;QACxC,2BAA2B,EAAE,YAAY;QACzC,wBAAwB,EAAE,YAAY;QACtC,2BAA2B,EAAE,aAAa;QAC1C,uBAAuB,EAAE;MAC3B;MACA,OAAOA,WAAW,CAACtB,KAAK,CAACoB,IAAI,KAAK,EAAC;IACrC;IAEA,MAAMG,aAAY,GAAIA,CAAA,KAAM;MAC1B,MAAMC,aAAY,GAAI;QACpB,YAAY,EAAE,IAAI;QAClB,qBAAqB,EAAE,WAAW;QAClC,wBAAwB,EAAE,WAAW;QACrC,4BAA4B,EAAE,WAAW;QACzC,kBAAkB,EAAE,WAAW;QAC/B,qBAAqB,EAAE,WAAW;QAClC,yBAAyB,EAAE,WAAW;QACtC,mBAAmB,EAAE,WAAW;QAChC,oBAAoB,EAAE,WAAW;QAEjC,kBAAkB,EAAE,WAAW;QAC/B,8BAA8B,EAAE,WAAW;QAC3C,qCAAqC,EAAE,WAAW;QAClD,gCAAgC,EAAE,WAAW;QAC7C,yBAAyB,EAAE,WAAW;QACtC,wBAAwB,EAAE,WAAW;QAErC;QACA,kBAAkB,EAAE,WAAW;QAC/B,wBAAwB,EAAE,kBAAkB;QAC5C,2BAA2B,EAAE,kBAAkB;QAC/C,wBAAwB,EAAE,kBAAkB;QAC5C,2BAA2B,EAAE,kBAAkB;QAC/C,uBAAuB,EAAE;MAC3B;MACA,OAAOA,aAAa,CAACxB,KAAK,CAACoB,IAAI,KAAK,IAAG;IACzC;IAEA,MAAMK,eAAc,GAAIA,CAAA,KAAM;MAC5B,IAAIhB,OAAO,CAACI,KAAK,EAAE,OAAO,KAAI;MAC9B,IAAIH,SAAS,CAACG,KAAK,EAAE,OAAO,IAAG;MAC/B,IAAIF,SAAS,CAACE,KAAK,EAAE,OAAO,IAAG;MAC/B,OAAO,IAAG;IACZ;IAEA,MAAMa,kBAAiB,GAAIA,CAAA,KAAM;MAC/BzB,gBAAgB,CAACY,KAAI,GAAI,CAACZ,gBAAgB,CAACY,KAAI;IACjD;IAIA,MAAMc,aAAY,GAAI,MAAOC,OAAO,IAAK;MACvC3B,gBAAgB,CAACY,KAAI,GAAI,KAAI;MAE7B,QAAQe,OAAO;QACb,KAAK,SAAS;UACZ7B,MAAM,CAAC8B,IAAI,CAAC,oBAAoB;UAChC;QACF,KAAK,QAAQ;UACX,IAAI;YACF,MAAM3D,YAAY,CAAC4D,OAAO,CACxB,WAAW,EACX,MAAM,EACN;cACEC,iBAAiB,EAAE,MAAM;cACzBC,gBAAgB,EAAE,IAAI;cACtBC,IAAI,EAAE,SAAS;cACfC,WAAW,EAAE;YACf,CACF;YAEA,MAAMpC,KAAK,CAACqC,QAAQ,CAAC,QAAQ;YAC7BlE,SAAS,CAACmE,OAAO,CAAC,SAAS;YAC3BrC,MAAM,CAAC8B,IAAI,CAAC,OAAO;UACrB,EAAE,OAAOQ,KAAK,EAAE;YACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;cACtBC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK;YACtC;UACF;UACA;MACJ;IACF;;IAEA;IACA,MAAME,gBAAe,GAAKC,KAAK,IAAK;MAClCF,OAAO,CAACG,GAAG,CAAC,QAAQ;MACpBD,KAAK,CAACE,MAAM,CAACC,KAAK,CAACC,OAAM,GAAI,OAAM;IACrC;;IAEA;IACA,MAAMC,iBAAgB,GAAKL,KAAK,IAAK;MACnCF,OAAO,CAACQ,IAAI,CAAC,eAAe;MAC5B;MACAN,KAAK,CAACE,MAAM,CAACC,KAAK,CAACC,OAAM,GAAI,MAAK;;MAElC;MACA,IAAI9C,KAAK,CAACiD,KAAK,CAACC,IAAG,IAAKlD,KAAK,CAACiD,KAAK,CAACC,IAAI,CAACC,MAAM,EAAE;QAC/CX,OAAO,CAACQ,IAAI,CAAC,YAAY,EAAEhD,KAAK,CAACiD,KAAK,CAACC,IAAI,CAACC,MAAM;QAClD;QACA;MACF;IACF;;IAEA;IACA,MAAMC,kBAAiB,GAAKV,KAAK,IAAK;MACpC,IAAI,CAACA,KAAK,CAACE,MAAM,CAACS,OAAO,CAAC,YAAY,CAAC,EAAE;QACvClD,gBAAgB,CAACY,KAAI,GAAI,KAAI;MAC/B;IACF;;IAEA;IACAjD,SAAS,CAAC,MAAM;MACdwF,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEH,kBAAkB;IACvD,CAAC;;IAED;IACArF,WAAW,CAAC,MAAM;MAChBuF,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEJ,kBAAkB;IAC1D,CAAC;IAED,OAAO;MACL3C,WAAW;MACXE,OAAO;MACPC,SAAS;MACTC,SAAS;MACTV,gBAAgB;MAChBC,gBAAgB;MAChB;MACAC,mBAAmB;MACnBC,gBAAgB;MAChBC,yBAAyB;MACzBC,oBAAoB;MACpB;MACAY,YAAY;MACZG,eAAe;MACfE,aAAa;MACbE,eAAe;MACfC,kBAAkB;MAClBd,aAAa;MACb;MACAE,iBAAiB;MACjBC,cAAc;MACdC,uBAAuB;MACvBC,kBAAkB;MAClBU,aAAa;MACb;MACAnC,YAAY;MACZC,UAAU;MACV8C,gBAAgB;MAChBM;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}