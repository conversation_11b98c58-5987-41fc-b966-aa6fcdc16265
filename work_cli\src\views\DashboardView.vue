<template>
  <div class="dashboard dashboard-layout">
    <div class="dashboard-container">
      <!-- 侧边栏 -->
      <aside class="sidebar" :class="{ collapsed: sidebarCollapsed }">
        <div class="sidebar-content">
          <!-- Logo区域 -->
          <div class="logo-section" v-show="!sidebarCollapsed">
            <div class="logo-content">
              <div class="logo-icon">
                <div class="logo-gradient">
                  <el-icon size="24"><Briefcase /></el-icon>
                </div>
              </div>
              <h3 class="logo-text">协作平台</h3>
            </div>
            <!-- 收起按钮 -->
            <button class="toggle-btn" @click="toggleSidebar">
              <el-icon>
                <transition name="icon-fade" mode="out-in">
                  <Fold key="fold" />
                </transition>
              </el-icon>
            </button>
          </div>

          <!-- 收起状态的展开按钮 -->
          <div class="collapsed-toggle" v-show="sidebarCollapsed">
            <button class="toggle-btn collapsed nav-item-style" @click="toggleSidebar" title="展开导航栏">
              <el-icon>
                <transition name="icon-fade" mode="out-in">
                  <Expand key="expand" />
                </transition>
              </el-icon>
            </button>
          </div>

          <!-- 导航菜单 -->
          <!-- 管理员专用导航 -->
          <AdminNavigation
            v-if="isAdmin"
            :sidebar-collapsed="sidebarCollapsed"
          />

          <!-- 普通用户导航 -->
          <nav v-else class="nav-menu">
            <!-- 仪表板 -->
            <div class="nav-section">
              <router-link
                to="/dashboard"
                class="nav-item"
                :class="{ active: $route.path === '/dashboard' }"
                :title="sidebarCollapsed ? '仪表板' : ''"
              >
                <el-icon><House /></el-icon>
                <transition name="fade">
                  <span v-show="!sidebarCollapsed">仪表板</span>
                </transition>
              </router-link>
            </div>

            <!-- 项目管理 -->
            <div class="nav-section">
              <div
                v-show="!sidebarCollapsed"
                class="nav-section-title expandable"
                @click="toggleProjectMenu"
                :class="{ expanded: projectMenuExpanded }"
              >
                <span>项目管理</span>
                <el-icon class="expand-icon">
                  <ArrowRight v-if="projectMenuExpanded" />
                  <ArrowDown v-else />
                </el-icon>
              </div>

              <!-- 项目管理子菜单 -->
              <div v-show="!sidebarCollapsed && projectMenuExpanded" class="nav-submenu-items">
                <router-link
                  v-if="isStudent"
                  to="/dashboard/projects"
                  class="nav-subitem"
                  :class="{ active: $route.path === '/dashboard/projects' }"
                >
                  <el-icon><Folder /></el-icon>
                  <span>项目浏览</span>
                </router-link>

                <!-- 我的项目（教师和学生都有） -->
                <router-link
                  v-if="isTeacher"
                  to="/dashboard/my-projects"
                  class="nav-subitem"
                  :class="{ active: $route.path === '/dashboard/my-projects' }"
                >
                  <el-icon><FolderOpened /></el-icon>
                  <span>我的项目</span>
                </router-link>

                <router-link
                  v-if="isStudent"
                  to="/dashboard/student-projects"
                  class="nav-subitem"
                  :class="{ active: $route.path === '/dashboard/student-projects' }"
                >
                  <el-icon><FolderOpened /></el-icon>
                  <span>我的项目</span>
                </router-link>

                <!-- 教师专用功能 -->
                <template v-if="isTeacher">
                  <router-link
                    to="/dashboard/projects/create"
                    class="nav-subitem"
                    :class="{ active: $route.path === '/dashboard/projects/create' }"
                  >
                    <el-icon><Plus /></el-icon>
                    <span>创建项目</span>
                  </router-link>
                </template>

                <!-- 评价中心（教师和学生都有） -->
                <router-link
                  to="/dashboard/evaluation-center"
                  class="nav-subitem"
                  :class="{ active: $route.path === '/dashboard/evaluation-center' }"
                >
                  <el-icon><Star /></el-icon>
                  <span>评价中心</span>
                </router-link>
              </div>

              <!-- 收起状态下的项目管理图标 -->
              <div v-show="sidebarCollapsed" class="collapsed-menu-icons">
                <router-link
                  v-if="isStudent"
                  to="/dashboard/projects"
                  class="nav-item"
                  :class="{ active: $route.path === '/dashboard/projects' }"
                  :title="'项目浏览'"
                >
                  <el-icon><Folder /></el-icon>
                </router-link>

                <!-- 我的项目（教师和学生都有） -->
                <router-link
                  v-if="isTeacher"
                  to="/dashboard/my-projects"
                  class="nav-item"
                  :class="{ active: $route.path === '/dashboard/my-projects' }"
                  :title="'我的项目'"
                >
                  <el-icon><FolderOpened /></el-icon>
                </router-link>

                <router-link
                  v-if="isStudent"
                  to="/dashboard/student-projects"
                  class="nav-item"
                  :class="{ active: $route.path === '/dashboard/student-projects' }"
                  :title="'我的项目'"
                >
                  <el-icon><FolderOpened /></el-icon>
                </router-link>

                <!-- 教师专用功能 -->
                <template v-if="isTeacher">
                  <router-link
                    to="/dashboard/projects/create"
                    class="nav-item"
                    :class="{ active: $route.path === '/dashboard/projects/create' }"
                    :title="'创建项目'"
                  >
                    <el-icon><Plus /></el-icon>
                  </router-link>
                </template>

                <!-- 评价中心（教师和学生都有） -->
                <router-link
                  to="/dashboard/evaluation-center"
                  class="nav-item"
                  :class="{ active: $route.path === '/dashboard/evaluation-center' }"
                  :title="'评价中心'"
                >
                  <el-icon><Star /></el-icon>
                </router-link>
              </div>
            </div>

            <!-- 团队管理 -->
            <div class="nav-section">
              <div
                v-show="!sidebarCollapsed"
                class="nav-section-title expandable"
                @click="toggleTeamMenu"
                :class="{ expanded: teamMenuExpanded }"
              >
                <span>团队管理</span>
                <el-icon class="expand-icon">
                  <ArrowRight v-if="teamMenuExpanded" />
                  <ArrowDown v-else />
                </el-icon>
              </div>

              <!-- 团队管理子菜单 -->
              <div v-show="!sidebarCollapsed && teamMenuExpanded" class="nav-submenu-items">
                <!-- 学生团队功能 -->
                <template v-if="isStudent">
                  <router-link
                    to="/dashboard/my-teams"
                    class="nav-subitem"
                    :class="{ active: $route.path === '/dashboard/my-teams' }"
                  >
                    <el-icon><User /></el-icon>
                    <span>我的团队</span>
                  </router-link>

                  <router-link
                    to="/dashboard/teams"
                    class="nav-subitem"
                    :class="{ active: $route.path === '/dashboard/teams' }"
                  >
                    <el-icon><UserFilled /></el-icon>
                    <span>浏览团队</span>
                  </router-link>

                  <router-link
                    to="/dashboard/teams/create"
                    class="nav-subitem"
                    :class="{ active: $route.path === '/dashboard/teams/create' }"
                  >
                    <el-icon><Plus /></el-icon>
                    <span>创建团队</span>
                  </router-link>
                </template>

                <!-- 教师查看团队 -->
                <template v-if="isTeacher">
                  <router-link
                    to="/dashboard/teams"
                    class="nav-subitem"
                    :class="{ active: $route.path === '/dashboard/teams' }"
                  >
                    <el-icon><UserFilled /></el-icon>
                    <span>团队管理</span>
                  </router-link>

                  <router-link
                    to="/dashboard/review"
                    class="nav-subitem"
                    :class="{ active: $route.path === '/dashboard/review' }"
                  >
                    <el-icon><DocumentChecked /></el-icon>
                    <span>申请审核</span>
                  </router-link>
                </template>
              </div>

              <!-- 收起状态下的团队管理图标 -->
              <div v-show="sidebarCollapsed" class="collapsed-menu-icons">
                <!-- 学生团队功能 -->
                <template v-if="isStudent">
                  <router-link
                    to="/dashboard/my-teams"
                    class="nav-item"
                    :class="{ active: $route.path === '/dashboard/my-teams' }"
                    :title="'我的团队'"
                  >
                    <el-icon><User /></el-icon>
                  </router-link>

                  <router-link
                    to="/dashboard/teams"
                    class="nav-item"
                    :class="{ active: $route.path === '/dashboard/teams' }"
                    :title="'浏览团队'"
                  >
                    <el-icon><UserFilled /></el-icon>
                  </router-link>

                  <router-link
                    to="/dashboard/teams/create"
                    class="nav-item"
                    :class="{ active: $route.path === '/dashboard/teams/create' }"
                    :title="'创建团队'"
                  >
                    <el-icon><Plus /></el-icon>
                  </router-link>
                </template>

                <!-- 教师查看团队 -->
                <template v-if="isTeacher">
                  <router-link
                    to="/dashboard/teams"
                    class="nav-item"
                    :class="{ active: $route.path === '/dashboard/teams' }"
                    :title="'团队管理'"
                  >
                    <el-icon><UserFilled /></el-icon>
                  </router-link>

                  <router-link
                    to="/dashboard/review"
                    class="nav-item"
                    :class="{ active: $route.path === '/dashboard/review' }"
                    :title="'申请审核'"
                  >
                    <el-icon><DocumentChecked /></el-icon>
                  </router-link>
                </template>
              </div>
            </div>

            <!-- 协作功能 -->
            <div class="nav-section">
              <div
                v-show="!sidebarCollapsed"
                class="nav-section-title expandable"
                @click="toggleCollaborationMenu"
                :class="{ expanded: collaborationMenuExpanded }"
              >
                <span>协作功能</span>
                <el-icon class="expand-icon">
                  <ArrowRight v-if="collaborationMenuExpanded" />
                  <ArrowDown v-else />
                </el-icon>
              </div>

              <!-- 协作功能子菜单 -->
              <div v-show="!sidebarCollapsed && collaborationMenuExpanded" class="nav-submenu-items">
                <router-link
                  to="/dashboard/collaboration/discussion"
                  class="nav-subitem"
                  :class="{ active: $route.path === '/dashboard/collaboration/discussion' }"
                >
                  <el-icon><ChatLineRound /></el-icon>
                  <span>项目讨论</span>
                </router-link>

                <router-link
                  to="/dashboard/collaboration/tasks"
                  class="nav-subitem"
                  :class="{ active: $route.path === '/dashboard/collaboration/tasks' }"
                >
                  <el-icon><List /></el-icon>
                  <span>任务管理</span>
                </router-link>

                <router-link
                  to="/dashboard/collaboration/space"
                  class="nav-subitem"
                  :class="{ active: $route.path.includes('/dashboard/collaboration/space') }"
                >
                  <el-icon><FolderOpened /></el-icon>
                  <span>协作空间</span>
                </router-link>

                <!-- 教师任务发布 -->
                <router-link
                  v-if="isTeacher"
                  to="/dashboard/task-publish"
                  class="nav-subitem"
                  :class="{ active: $route.path === '/dashboard/task-publish' }"
                >
                  <el-icon><Plus /></el-icon>
                  <span>发布任务</span>
                </router-link>

                <!-- 教师任务审核 -->
                <router-link
                  v-if="isTeacher"
                  to="/dashboard/task-review"
                  class="nav-subitem"
                  :class="{ active: $route.path === '/dashboard/task-review' }"
                >
                  <el-icon><DocumentChecked /></el-icon>
                  <span>任务审核</span>
                </router-link>



                <router-link
                  to="/dashboard/file-management"
                  class="nav-subitem"
                  :class="{ active: $route.path === '/dashboard/file-management' }"
                  v-if="false"
                >
                  <el-icon><FolderOpened /></el-icon>
                  <span>文件管理</span>
                </router-link>


              </div>

              <!-- 收起状态下的协作功能图标 -->
              <div v-show="sidebarCollapsed" class="collapsed-menu-icons">
                <router-link
                  to="/dashboard/collaboration/discussion"
                  class="nav-item"
                  :class="{ active: $route.path === '/dashboard/collaboration/discussion' }"
                  :title="'项目讨论'"
                >
                  <el-icon><ChatLineRound /></el-icon>
                </router-link>

                <router-link
                  to="/dashboard/collaboration/tasks"
                  class="nav-item"
                  :class="{ active: $route.path === '/dashboard/collaboration/tasks' }"
                  :title="'任务管理'"
                >
                  <el-icon><List /></el-icon>
                </router-link>

                <router-link
                  to="/dashboard/collaboration/space"
                  class="nav-item"
                  :class="{ active: $route.path.includes('/dashboard/collaboration/space') }"
                  :title="'协作空间'"
                >
                  <el-icon><FolderOpened /></el-icon>
                </router-link>

                <!-- 教师任务发布 -->
                <router-link
                  v-if="isTeacher"
                  to="/dashboard/task-publish"
                  class="nav-item"
                  :class="{ active: $route.path === '/dashboard/task-publish' }"
                  :title="'发布任务'"
                >
                  <el-icon><Plus /></el-icon>
                </router-link>

                <!-- 教师任务审核 -->
                <router-link
                  v-if="isTeacher"
                  to="/dashboard/task-review"
                  class="nav-item"
                  :class="{ active: $route.path === '/dashboard/task-review' }"
                  :title="'任务审核'"
                >
                  <el-icon><DocumentChecked /></el-icon>
                </router-link>



                <router-link
                  to="/dashboard/file-management"
                  class="nav-item"
                  :class="{ active: $route.path === '/dashboard/file-management' }"
                  :title="'文件管理'"
                  v-if="false"
                >
                  <el-icon><FolderOpened /></el-icon>
                </router-link>


              </div>
            </div>







            <!-- 系统设置 -->
            <div class="nav-section">
              <div
                v-show="!sidebarCollapsed"
                class="nav-section-title expandable"
                @click="toggleSettingsMenu"
                :class="{ expanded: settingsMenuExpanded }"
              >
                <span>系统设置</span>
                <el-icon class="expand-icon">
                  <ArrowRight v-if="settingsMenuExpanded" />
                  <ArrowDown v-else />
                </el-icon>
              </div>

              <!-- 系统设置子菜单 -->
              <div v-show="!sidebarCollapsed && settingsMenuExpanded" class="nav-submenu-items">
                <router-link
                  to="/dashboard/profile"
                  class="nav-subitem"
                  :class="{ active: $route.path === '/dashboard/profile' }"
                >
                  <el-icon><Setting /></el-icon>
                  <span>个人设置</span>
                </router-link>
              </div>

              <!-- 收起状态下的系统设置图标 -->
              <div v-show="sidebarCollapsed" class="collapsed-menu-icons">
                <router-link
                  to="/dashboard/profile"
                  class="nav-item"
                  :class="{ active: $route.path === '/dashboard/profile' }"
                  :title="'个人设置'"
                >
                  <el-icon><Setting /></el-icon>
                </router-link>
              </div>
            </div>
          </nav>
        </div>
      </aside>

      <!-- 主内容区 -->
      <main class="main-content">
        <!-- 顶部导航栏 -->
        <header class="top-header">
          <div class="header-content">
            <div class="header-left">
              <div class="breadcrumb">
                <el-icon class="breadcrumb-icon"><Location /></el-icon>
                <span class="breadcrumb-text">{{ getBreadcrumb() }}</span>
              </div>
              <h1 class="page-title">{{ getPageTitle() }}</h1>
              <p class="page-subtitle">{{ getPageSubtitle() }}</p>
            </div>

            <div class="header-right">
              <!-- 通知中心 -->
              <!-- <NotificationCenter /> -->

              <!-- 用户下拉菜单 -->
              <div class="user-menu" @click="toggleUserDropdown">
                <div class="user-info">
                  <div class="avatar">
                    <img v-if="getAvatarUrl(currentUser?.avatar)" :src="getAvatarUrl(currentUser?.avatar)" :alt="currentUser?.realName" @error="handleAvatarError" @load="handleAvatarLoad" />
                    <span v-else>{{ getInitial(currentUser?.realName) }}</span>
                  </div>
                  <div class="user-details">
                    <span class="user-name">{{ currentUser?.realName }}</span>
                    <span class="user-role">{{ getUserRoleText() }}</span>
                  </div>
                  <el-icon class="dropdown-arrow" :class="{ rotated: showUserDropdown }">
                    <ArrowDown />
                  </el-icon>
                </div>

                <!-- 下拉菜单 -->
                <div class="dropdown-content" :class="{ active: showUserDropdown }">
                  <div class="dropdown-item" @click.prevent="handleCommand('profile')">
                    <el-icon><User /></el-icon>
                    <span>个人资料</span>
                  </div>
                  <div class="dropdown-divider"></div>
                  <div class="dropdown-item logout" @click.prevent="handleCommand('logout')">
                    <el-icon><SwitchButton /></el-icon>
                    <span>退出登录</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- 页面内容 -->
        <div class="page-content">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  House, Folder, FolderOpened, Plus, DocumentChecked,
  UserFilled, User, Setting, ArrowDown, ArrowRight, SwitchButton,
  ChatDotRound, ChatLineRound, List, Document,
  Briefcase, Star, Expand, Fold, Location, Monitor
} from '@element-plus/icons-vue'
import { getAvatarUrl, getInitial } from '@/utils/avatar'
import AdminNavigation from '@/components/AdminNavigation.vue'
// import NotificationCenter from '@/components/NotificationCenter.vue'

export default {
  name: 'DashboardView',
  components: {
    AdminNavigation,
    House,
    Folder,
    FolderOpened,
    Plus,
    DocumentChecked,
    UserFilled,
    User,
    Setting,
    ArrowDown,
    ArrowRight,
    SwitchButton,
    ChatDotRound,
    ChatLineRound,
    List,
    Document,
    Briefcase,
    Star,
    Expand,
    Fold,
    Location,
    Monitor
    // NotificationCenter
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()

    const showUserDropdown = ref(false)
    const sidebarCollapsed = ref(false)

    // 各个菜单的展开状态，默认都展开
    const projectMenuExpanded = ref(true)
    const teamMenuExpanded = ref(true)
    const collaborationMenuExpanded = ref(true)
    const settingsMenuExpanded = ref(true)

    const currentUser = computed(() => store.getters.currentUser)
    const isAdmin = computed(() => store.getters.isAdmin)
    const isTeacher = computed(() => store.getters.isTeacher)
    const isStudent = computed(() => store.getters.isStudent)

    const toggleSidebar = () => {
      sidebarCollapsed.value = !sidebarCollapsed.value
    }

    // 各个菜单的切换方法
    const toggleProjectMenu = () => {
      projectMenuExpanded.value = !projectMenuExpanded.value
    }

    const toggleTeamMenu = () => {
      teamMenuExpanded.value = !teamMenuExpanded.value
    }

    const toggleCollaborationMenu = () => {
      collaborationMenuExpanded.value = !collaborationMenuExpanded.value
    }

    const toggleSettingsMenu = () => {
      settingsMenuExpanded.value = !settingsMenuExpanded.value
    }

    const getPageTitle = () => {
      const routeMap = {
        '/dashboard': '仪表板',
        '/dashboard/projects': '项目列表',
        '/dashboard/my-projects': '我的项目',
        '/dashboard/projects/create': '创建项目',
        '/dashboard/teams': '团队列表',
        '/dashboard/my-teams': '我的团队',
        '/dashboard/teams/create': '创建团队',
        '/dashboard/review': '申请审核',
        '/dashboard/profile': '个人设置',

        '/dashboard/files': '文件管理',
        '/dashboard/evaluation-center': '评价中心',
        '/dashboard/collaboration/discussion': '项目讨论',
        '/dashboard/collaboration/tasks': '任务管理',
        '/dashboard/collaboration/space': '协作空间',
        '/dashboard/task-publish': '发布任务',
        '/dashboard/task-review': '任务审核',

        // 管理员页面
        '/dashboard/admin': '管理仪表板',
        '/dashboard/admin/users': '用户管理',
        '/dashboard/admin/projects': '项目管理',
        '/dashboard/admin/teams': '团队管理',
        '/dashboard/admin/settings': '系统设置',
        '/dashboard/admin/logs': '系统日志'
      }
      return routeMap[route.path] || '项目协作平台'
    }

    const getPageSubtitle = () => {
      const subtitleMap = {
        '/dashboard': '欢迎回来，查看您的项目概览',
        '/dashboard/projects': '浏览所有可用的项目',
        '/dashboard/my-projects': '管理您创建的项目',
        '/dashboard/projects/create': '创建新的协作项目',
        '/dashboard/teams': '查看所有团队信息',
        '/dashboard/my-teams': '管理您的团队',
        '/dashboard/teams/create': '组建新的项目团队',
        '/dashboard/review': '审核学生的申请',
        '/dashboard/profile': '管理您的个人信息',

        '/dashboard/files': '管理和下载项目文件',
        '/dashboard/test-data': '测试系统数据连接',
        '/dashboard/collaboration/discussion': '与团队成员讨论项目',
        '/dashboard/collaboration/tasks': '管理项目任务和进度',
        '/dashboard/task-publish': '为项目团队发布新任务',
        '/dashboard/task-review': '审核学生提交的任务',

        // 管理员页面副标题
        '/dashboard/admin': '查看系统整体运行状态和统计信息',
        '/dashboard/admin/users': '管理系统中的所有用户账户',
        '/dashboard/admin/projects': '管理系统中的所有项目',
        '/dashboard/admin/teams': '管理系统中的所有团队',
        '/dashboard/admin/settings': '配置系统参数和业务规则',
        '/dashboard/admin/logs': '查看系统操作日志和错误记录'
      }
      return subtitleMap[route.path] || ''
    }

    const getBreadcrumb = () => {
      const breadcrumbMap = {
        '/dashboard': '首页',
        '/dashboard/projects': '首页 / 项目列表',
        '/dashboard/my-projects': '首页 / 我的项目',
        '/dashboard/projects/create': '首页 / 创建项目',
        '/dashboard/teams': '首页 / 团队列表',
        '/dashboard/my-teams': '首页 / 我的团队',
        '/dashboard/teams/create': '首页 / 创建团队',
        '/dashboard/review': '首页 / 申请审核',
        '/dashboard/profile': '首页 / 个人设置',

        '/dashboard/files': '首页 / 文件管理',
        '/dashboard/evaluation-center': '首页 / 评价中心',
        '/dashboard/collaboration/discussion': '首页 / 项目讨论',
        '/dashboard/collaboration/tasks': '首页 / 任务管理',
        '/dashboard/task-publish': '首页 / 发布任务',
        '/dashboard/task-review': '首页 / 任务审核',

        // 管理员面包屑
        '/dashboard/admin': '首页 / 系统管理',
        '/dashboard/admin/users': '首页 / 系统管理 / 用户管理',
        '/dashboard/admin/projects': '首页 / 系统管理 / 项目管理',
        '/dashboard/admin/teams': '首页 / 系统管理 / 团队管理',
        '/dashboard/admin/settings': '首页 / 系统管理 / 系统设置',
        '/dashboard/admin/logs': '首页 / 系统管理 / 系统日志'
      }
      return breadcrumbMap[route.path] || '首页'
    }

    const getUserRoleText = () => {
      if (isAdmin.value) return '管理员'
      if (isTeacher.value) return '教师'
      if (isStudent.value) return '学生'
      return '用户'
    }

    const toggleUserDropdown = () => {
      showUserDropdown.value = !showUserDropdown.value
    }



    const handleCommand = async (command) => {
      showUserDropdown.value = false

      switch (command) {
        case 'profile':
          router.push('/dashboard/profile')
          break
        case 'logout':
          try {
            await ElMessageBox.confirm(
              '确定要退出登录吗？',
              '退出确认',
              {
                confirmButtonText: '确定退出',
                cancelButtonText: '取消',
                type: 'warning',
                customClass: 'soft-message-box'
              }
            )

            await store.dispatch('logout')
            ElMessage.success('已安全退出登录')
            router.push('/auth')
          } catch (error) {
            if (error !== 'cancel') {
              console.error('Logout error:', error)
            }
          }
          break
      }
    }

    // 头像加载成功处理
    const handleAvatarLoad = (event) => {
      console.log('头像加载成功')
      event.target.style.display = 'block'
    }

    // 头像加载错误处理
    const handleAvatarError = (event) => {
      console.warn('头像加载失败，使用默认头像')
      // 隐藏图片元素，显示默认字母头像
      event.target.style.display = 'none'

      // 清理无效的头像URL，避免重复请求
      if (store.state.user && store.state.user.avatar) {
        console.warn('清理无效头像URL:', store.state.user.avatar)
        // 可以考虑清理store中的无效头像URL
        // store.commit('SET_USER', { ...store.state.user, avatar: null })
      }
    }

    // 点击外部关闭下拉菜单
    const handleClickOutside = (event) => {
      if (!event.target.closest('.user-menu')) {
        showUserDropdown.value = false
      }
    }

    // 监听点击事件
    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
    })

    // 组件卸载时移除事件监听器
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })

    return {
      currentUser,
      isAdmin,
      isTeacher,
      isStudent,
      showUserDropdown,
      sidebarCollapsed,
      // 菜单展开状态
      projectMenuExpanded,
      teamMenuExpanded,
      collaborationMenuExpanded,
      settingsMenuExpanded,
      // 方法
      getPageTitle,
      getPageSubtitle,
      getBreadcrumb,
      getUserRoleText,
      toggleUserDropdown,
      toggleSidebar,
      // 菜单切换方法
      toggleProjectMenu,
      toggleTeamMenu,
      toggleCollaborationMenu,
      toggleSettingsMenu,
      handleCommand,
      // 头像相关方法
      getAvatarUrl,
      getInitial,
      handleAvatarLoad,
      handleAvatarError
    }
  }
}
</script>

<style scoped>
.dashboard {
  height: 100vh;
  background: var(--background-color);
}

.dashboard-container {
  display: flex;
  height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
  width: 280px;
  background: #ffffff;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0;
  overflow: hidden;
  position: relative;
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-right: 1px solid rgba(0, 0, 0, 0.08);
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-content {
  height: 100%;
  padding: var(--space-6);
  overflow-y: auto;
  overflow-x: hidden;
}

/* Logo区域 */
.logo-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-8);
  padding: var(--space-6) var(--space-5);
  background: transparent;
  border-radius: 0;
  border: none;
  position: relative;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.sidebar.collapsed .logo-section {
  justify-content: center;
  padding: var(--space-3);
}

.logo-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.sidebar.collapsed .logo-content {
  justify-content: center;
}

.logo-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.logo-icon:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.logo-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-icon-only {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
  margin: 0 auto;
}

.logo-icon-only:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

/* 侧边栏切换按钮 */
.toggle-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  color: #6366f1;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.toggle-btn .el-icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-btn:active {
  transform: scale(0.95);
}

.toggle-btn:active .el-icon {
  transform: scale(0.9);
}

.collapsed-toggle {
  padding: 0;
  margin: 0 4px 16px 4px;
  display: flex;
  justify-content: center;
}

.toggle-btn.collapsed.nav-item-style {
  width: 100%;
  height: auto;
  background: transparent;
  border-radius: 12px;
  box-shadow: none;
  padding: 12px;
  justify-content: center;
  align-items: center;
  color: #64748b;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-btn.collapsed.nav-item-style .el-icon {
  font-size: 16px;
}

.toggle-btn:hover {
  background: #6366f1;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.toggle-btn:hover .el-icon {
  transform: scale(1.1);
}

.sidebar.collapsed .toggle-btn.nav-item-style:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
  color: #6366f1;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

.sidebar.collapsed .toggle-btn.nav-item-style:active {
  transform: scale(0.95);
}

/* 导航菜单 */
.nav-menu {
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.nav-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.nav-section-title {
  font-size: 11px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  margin-bottom: var(--space-3);
  padding: 0 var(--space-4);
  transition: opacity 0.3s ease;
}

.nav-section-title.expandable {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: var(--space-4) var(--space-5);
  border-radius: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: transparent;
  border: none;
  margin-bottom: var(--space-1);
  position: relative;
  font-weight: var(--font-weight-semibold);
  font-size: var(--text-sm);
  color: var(--gray-700);
  letter-spacing: 0.025em;
}

.nav-section-title.expandable::after {
  content: '';
  position: absolute;
  left: var(--space-5);
  right: var(--space-5);
  bottom: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--gray-300), transparent);
  opacity: 0.6;
}

.nav-section-title.expandable:hover {
  color: var(--gray-900);
  background: rgba(0, 0, 0, 0.02);
}

.nav-section-title.expandable.expanded {
  color: var(--primary-600);
  background: rgba(99, 102, 241, 0.04);
}

.nav-section-title.expandable.expanded::after {
  background: linear-gradient(90deg, transparent, var(--primary-300), transparent);
}

.expand-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  color: var(--gray-500);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  background: transparent;
}

.nav-section-title.expandable:hover .expand-icon {
  color: var(--gray-700);
  background: rgba(0, 0, 0, 0.05);
}

.nav-section-title.expandable.expanded .expand-icon {
  transform: rotate(90deg);
  color: var(--primary-600);
  background: rgba(99, 102, 241, 0.1);
}

.nav-submenu-items {
  margin-left: 0;
  border-left: none;
  padding-left: 0;
  margin-top: var(--space-2);
  position: relative;
  background: transparent;
  border-radius: 0;
  padding-top: 0;
  padding-bottom: var(--space-3);
}

.nav-subitem {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2_5) var(--space-5);
  color: var(--gray-500);
  text-decoration: none;
  border-radius: 0;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-regular);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 0;
  border: none;
  position: relative;
  background: transparent;
  padding-left: calc(var(--space-5) + var(--space-4));
}

.nav-subitem::before {
  content: '';
  position: absolute;
  left: calc(var(--space-5) + var(--space-2));
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  border-radius: var(--radius-full);
  background: var(--gray-400);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-subitem:hover {
  background: rgba(0, 0, 0, 0.02);
  color: var(--gray-700);
  padding-left: calc(var(--space-5) + var(--space-5));
}

.nav-subitem:hover::before {
  background: var(--primary-500);
  transform: translateY(-50%) scale(1.5);
}

.nav-subitem.active {
  background: rgba(99, 102, 241, 0.06);
  color: var(--primary-700);
  font-weight: var(--font-weight-medium);
}

.nav-subitem.active::before {
  background: var(--primary-500);
  transform: translateY(-50%) scale(1.5);
}

.collapsed-menu-icons {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-5);
  border-radius: 0;
  color: var(--gray-600);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  font-size: var(--text-sm);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  margin: 0;
  border: none;
  background: transparent;
}

.sidebar.collapsed .nav-item {
  justify-content: center;
  padding: 12px;
  margin: 0 8px;
}

.nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--primary-500);
  transform: scaleY(0);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0 2px 2px 0;
}

.nav-item:hover {
  background: rgba(0, 0, 0, 0.03);
  color: var(--gray-900);
  padding-left: calc(var(--space-5) + var(--space-1));
}

.nav-item.active {
  background: rgba(99, 102, 241, 0.08);
  color: var(--primary-700);
  font-weight: var(--font-weight-semibold);
}

.nav-item.active::before {
  transform: scaleY(1);
}

.nav-item.active:hover {
  background: rgba(99, 102, 241, 0.12);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 24px;
  background: var(--primary-gradient);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
  box-shadow: var(--shadow-sm);
}

.sidebar.collapsed .nav-item.active::before {
  display: none;
}

/* 过渡动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 图标切换动画 */
.icon-fade-enter-active, .icon-fade-leave-active {
  transition: all 0.2s ease;
}

.icon-fade-enter-from {
  opacity: 0;
  transform: scale(0.8) rotate(90deg);
}

.icon-fade-leave-to {
  opacity: 0;
  transform: scale(0.8) rotate(-90deg);
}

/* 子菜单 */
.nav-submenu {
  margin-left: var(--space-4);
}

.nav-submenu-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.nav-submenu-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  margin-left: var(--space-6);
  padding-left: var(--space-4);
  border-left: 2px solid rgba(160, 118, 249, 0.2);
}

.nav-subitem {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: all var(--transition-base);
}

.nav-subitem:hover {
  background: rgba(160, 118, 249, 0.1);
  color: var(--primary-gradient-start);
}

.nav-subitem.active {
  background: var(--primary-gradient);
  color: var(--text-on-primary);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 顶部导航栏 */
.top-header {
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0;
  margin: 0;
  position: relative;
  z-index: 5;
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-5) 0;
  width: 100%;
  min-height: 80px;
}

.header-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-left: var(--space-6);
}

.header-right {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  padding-right: var(--space-6);
}

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
  padding: 0;
  background: transparent;
  border: none;
  width: fit-content;
}

.breadcrumb-icon {
  color: var(--gray-400);
  font-size: 14px;
  margin-right: var(--space-1);
}

.breadcrumb-text {
  font-size: var(--text-xs);
  color: var(--gray-500);
  font-weight: var(--font-weight-medium);
  font-family: var(--font-sans);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0 0 var(--space-1) 0;
  background: none;
  line-height: 1.3;
  font-family: var(--font-sans);
  position: relative;
  letter-spacing: -0.01em;
}

.page-subtitle {
  font-size: var(--text-sm);
  color: var(--gray-500);
  margin: 0;
  font-weight: var(--font-weight-regular);
  font-family: var(--font-sans);
  line-height: var(--leading-relaxed);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

/* 通知按钮 */
.notification-btn {
  position: relative;
  width: 44px;
  height: 44px;
  border: none;
  border-radius: var(--radius-full);
  background: var(--background-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-btn:hover {
  background: var(--primary-gradient);
  color: var(--text-on-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-soft-colored);
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 18px;
  height: 18px;
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--surface-color);
}

/* 用户菜单 */
.user-menu {
  position: relative;
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  background: transparent;
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: none;
}

.user-info:hover {
  background: rgba(0, 0, 0, 0.02);
  border-color: rgba(0, 0, 0, 0.12);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: var(--text-on-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--text-sm);
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-right: var(--space-2);
}

.user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  line-height: var(--leading-tight);
  font-family: var(--font-sans);
  margin-bottom: 2px;
}

.user-role {
  font-size: var(--text-xs);
  color: var(--gray-500);
  line-height: var(--leading-tight);
  font-family: var(--font-sans);
  font-weight: var(--font-weight-regular);
}

.dropdown-arrow {
  color: var(--gray-400);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
}

.user-info:hover .dropdown-arrow {
  color: var(--gray-600);
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

/* 下拉菜单 */
.dropdown-content {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  min-width: 200px;
  background: #ffffff;
  border-radius: var(--radius-xl);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: var(--space-2);
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.dropdown-content.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  color: var(--gray-700);
  text-decoration: none;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  font-size: var(--text-sm);
  font-family: var(--font-sans);
  font-weight: var(--font-weight-medium);
  border: none;
  position: relative;
  background: transparent;
}

.dropdown-item:hover {
  background: rgba(0, 0, 0, 0.04);
  color: var(--gray-900);
}

.dropdown-item .el-icon {
  font-size: 16px;
  color: var(--gray-500);
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-item:hover .el-icon {
  color: var(--gray-700);
}

.dropdown-item.logout {
  color: var(--error-600);
}

.dropdown-item.logout:hover {
  background: rgba(239, 68, 68, 0.08);
  color: var(--error-700);
}

.dropdown-item.logout .el-icon {
  color: var(--error-500);
}

.dropdown-item.logout:hover .el-icon {
  color: var(--error-600);
}

.dropdown-divider {
  height: 1px;
  background: rgba(0, 0, 0, 0.08);
  margin: var(--space-1) 0;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: var(--space-6) var(--space-4) var(--space-4) var(--space-4);
  overflow-y: auto;
  overflow-x: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 240px;
  }

  .header-content {
    padding: var(--space-4) var(--space-6);
  }

  .page-title {
    font-size: 20px;
  }

  .user-details {
    display: none;
  }

  .page-content {
    padding: var(--space-4);
  }
}

@media (max-width: 640px) {
  .dashboard-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    border-radius: 0;
  }

  .sidebar-content {
    padding: var(--space-4);
  }

  .nav-menu {
    flex-direction: row;
    overflow-x: auto;
    gap: var(--space-2);
  }

  .nav-section {
    flex-direction: row;
    min-width: max-content;
  }

  .nav-section-title {
    display: none;
  }
}
</style>
