<template>
  <nav class="admin-nav-menu">
    <!-- 管理仪表板 -->
    <div class="nav-section">
      <router-link
        to="/dashboard/admin"
        class="nav-item"
        :class="{ active: $route.path === '/dashboard/admin' }"
        :title="sidebarCollapsed ? '管理仪表板' : ''"
      >
        <el-icon><Monitor /></el-icon>
        <transition name="fade">
          <span v-show="!sidebarCollapsed">管理仪表板</span>
        </transition>
      </router-link>
    </div>

    <!-- 用户管理 -->
    <div class="nav-section">
      <div
        v-show="!sidebarCollapsed"
        class="nav-section-title expandable"
        @click="toggleUserMenu"
        :class="{ expanded: userMenuExpanded }"
      >
        <span>用户管理</span>
        <el-icon class="expand-icon">
          <ArrowRight v-if="userMenuExpanded" />
          <ArrowDown v-else />
        </el-icon>
      </div>

      <div v-show="!sidebarCollapsed && userMenuExpanded" class="nav-submenu-items">
        <router-link
          to="/dashboard/admin/users"
          class="nav-subitem"
          :class="{ active: $route.path === '/dashboard/admin/users' }"
        >
          <el-icon><User /></el-icon>
          <span>用户管理</span>
        </router-link>
      </div>

      <div v-show="sidebarCollapsed" class="collapsed-menu-icons">
        <router-link
          to="/dashboard/admin/users"
          class="nav-item"
          :class="{ active: $route.path === '/dashboard/admin/users' }"
          :title="'用户管理'"
        >
          <el-icon><User /></el-icon>
        </router-link>
      </div>
    </div>

    <!-- 内容管理 -->
    <div class="nav-section">
      <div
        v-show="!sidebarCollapsed"
        class="nav-section-title expandable"
        @click="toggleContentMenu"
        :class="{ expanded: contentMenuExpanded }"
      >
        <span>内容管理</span>
        <el-icon class="expand-icon">
          <ArrowRight v-if="contentMenuExpanded" />
          <ArrowDown v-else />
        </el-icon>
      </div>

      <div v-show="!sidebarCollapsed && contentMenuExpanded" class="nav-submenu-items">
        <router-link
          to="/dashboard/admin/projects"
          class="nav-subitem"
          :class="{ active: $route.path === '/dashboard/admin/projects' }"
        >
          <el-icon><Folder /></el-icon>
          <span>项目管理</span>
        </router-link>

        <router-link
          to="/dashboard/admin/teams"
          class="nav-subitem"
          :class="{ active: $route.path === '/dashboard/admin/teams' }"
        >
          <el-icon><UserFilled /></el-icon>
          <span>团队管理</span>
        </router-link>
      </div>

      <div v-show="sidebarCollapsed" class="collapsed-menu-icons">
        <router-link
          to="/dashboard/admin/projects"
          class="nav-item"
          :class="{ active: $route.path === '/dashboard/admin/projects' }"
          :title="'项目管理'"
        >
          <el-icon><Folder /></el-icon>
        </router-link>

        <router-link
          to="/dashboard/admin/teams"
          class="nav-item"
          :class="{ active: $route.path === '/dashboard/admin/teams' }"
          :title="'团队管理'"
        >
          <el-icon><UserFilled /></el-icon>
        </router-link>
      </div>
    </div>

    <!-- 系统配置 -->
    <div class="nav-section">
      <div
        v-show="!sidebarCollapsed"
        class="nav-section-title expandable"
        @click="toggleSystemMenu"
        :class="{ expanded: systemMenuExpanded }"
      >
        <span>系统配置</span>
        <el-icon class="expand-icon">
          <ArrowRight v-if="systemMenuExpanded" />
          <ArrowDown v-else />
        </el-icon>
      </div>

      <div v-show="!sidebarCollapsed && systemMenuExpanded" class="nav-submenu-items">
        <router-link
          to="/dashboard/admin/announcements"
          class="nav-subitem"
          :class="{ active: $route.path === '/dashboard/admin/announcements' }"
        >
          <el-icon><Bell /></el-icon>
          <span>公告管理</span>
        </router-link>
      </div>

      <div v-show="sidebarCollapsed" class="collapsed-menu-icons">
        <router-link
          to="/dashboard/admin/announcements"
          class="nav-item"
          :class="{ active: $route.path === '/dashboard/admin/announcements' }"
          :title="'公告管理'"
        >
          <el-icon><Bell /></el-icon>
        </router-link>
      </div>
    </div>

    <!-- 个人设置 -->
    <div class="nav-section">
      <div
        v-show="!sidebarCollapsed"
        class="nav-section-title expandable"
        @click="togglePersonalMenu"
        :class="{ expanded: personalMenuExpanded }"
      >
        <span>个人设置</span>
        <el-icon class="expand-icon">
          <ArrowRight v-if="personalMenuExpanded" />
          <ArrowDown v-else />
        </el-icon>
      </div>

      <div v-show="!sidebarCollapsed && personalMenuExpanded" class="nav-submenu-items">
        <router-link
          to="/dashboard/admin/profile"
          class="nav-subitem"
          :class="{ active: $route.path === '/dashboard/admin/profile' }"
        >
          <el-icon><User /></el-icon>
          <span>个人资料</span>
        </router-link>
      </div>

      <div v-show="sidebarCollapsed" class="collapsed-menu-icons">
        <router-link
          to="/dashboard/admin/profile"
          class="nav-item"
          :class="{ active: $route.path === '/dashboard/admin/profile' }"
          :title="'个人资料'"
        >
          <el-icon><User /></el-icon>
        </router-link>
      </div>
    </div>
  </nav>
</template>

<script>
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import {
  Monitor,
  User,
  Folder,
  UserFilled,
  Setting,
  Document,
  ArrowDown,
  ArrowRight,
  Bell
} from '@element-plus/icons-vue'

export default {
  name: 'AdminNavigation',
  components: {
    Monitor,
    User,
    Folder,
    UserFilled,
    Setting,
    Document,
    ArrowDown,
    ArrowRight,
    Bell
  },
  props: {
    sidebarCollapsed: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const route = useRoute()
    
    // 各个菜单的展开状态，默认都展开
    const userMenuExpanded = ref(true)
    const contentMenuExpanded = ref(true)
    const systemMenuExpanded = ref(true)
    const personalMenuExpanded = ref(true)
    
    const toggleUserMenu = () => {
      userMenuExpanded.value = !userMenuExpanded.value
    }
    
    const toggleContentMenu = () => {
      contentMenuExpanded.value = !contentMenuExpanded.value
    }
    
    const toggleSystemMenu = () => {
      systemMenuExpanded.value = !systemMenuExpanded.value
    }
    
    const togglePersonalMenu = () => {
      personalMenuExpanded.value = !personalMenuExpanded.value
    }
    
    return {
      route,
      userMenuExpanded,
      contentMenuExpanded,
      systemMenuExpanded,
      personalMenuExpanded,
      toggleUserMenu,
      toggleContentMenu,
      toggleSystemMenu,
      togglePersonalMenu
    }
  }
}
</script>

<style>
/* 管理员导航样式 - 与用户端导航保持一致 */
.admin-nav-menu {
  /* 定义CSS变量 */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-2_5: 10px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-full: 50%;
  --text-sm: 14px;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-900: #111827;
  --gray-300: #d1d5db;
  --primary-300: #a5b4fc;
  --primary-500: #6366f1;
  --primary-600: #4f46e5;
  --primary-700: #4338ca;

  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.admin-nav-menu .nav-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.admin-nav-menu .nav-section-title {
  font-size: 11px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  margin-bottom: var(--space-3);
  padding: 0 var(--space-4);
  transition: opacity 0.3s ease;
}

.admin-nav-menu .nav-section-title.expandable {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: var(--space-4) var(--space-5);
  margin: 0;
  border-radius: var(--radius-md);
  background: transparent;
  border: none;
  position: relative;
  font-weight: 500;
  font-size: var(--text-sm);
  color: var(--gray-700);
  letter-spacing: 0.025em;
}

.admin-nav-menu .nav-section-title.expandable::after {
  content: '';
  position: absolute;
  left: var(--space-5);
  right: var(--space-5);
  bottom: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--gray-300), transparent);
  opacity: 0.6;
}

.admin-nav-menu .nav-section-title.expandable:hover {
  color: var(--gray-900);
  background: rgba(0, 0, 0, 0.02);
}

.admin-nav-menu .nav-section-title.expandable.expanded {
  color: var(--primary-600);
  background: rgba(99, 102, 241, 0.04);
}

.admin-nav-menu .nav-section-title.expandable.expanded::after {
  background: linear-gradient(90deg, transparent, var(--primary-300), transparent);
}

.admin-nav-menu .expand-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  color: var(--gray-500);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  background: transparent;
}

.admin-nav-menu .nav-section-title.expandable:hover .expand-icon {
  color: var(--gray-700);
  background: rgba(0, 0, 0, 0.05);
}

.admin-nav-menu .nav-section-title.expandable.expanded .expand-icon {
  transform: rotate(90deg);
  color: var(--primary-600);
  background: rgba(99, 102, 241, 0.1);
}

.admin-nav-menu .nav-submenu-items {
  margin-left: 0;
  border-left: none;
  padding-left: 0;
  margin-top: var(--space-2);
  position: relative;
  background: transparent;
  border-radius: 0;
  padding-top: 0;
  padding-bottom: var(--space-3);
}

.admin-nav-menu .nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-5);
  border-radius: 0;
  color: var(--gray-600);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  font-size: var(--text-sm);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  margin: 0;
  border: none;
  background: transparent;
}

.admin-nav-menu .nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--primary-500);
  transform: scaleY(0);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0 2px 2px 0;
}

.admin-nav-menu .nav-item:hover {
  background: rgba(0, 0, 0, 0.03);
  color: var(--gray-900);
  padding-left: calc(var(--space-5) + var(--space-1));
}

.admin-nav-menu .nav-item.active {
  background: rgba(99, 102, 241, 0.08);
  color: var(--primary-700);
  font-weight: var(--font-weight-semibold);
}

.admin-nav-menu .nav-item.active::before {
  transform: scaleY(1);
}

.admin-nav-menu .nav-item.active:hover {
  background: rgba(99, 102, 241, 0.12);
}

.admin-nav-menu .nav-submenu-items {
  margin-left: 0;
  border-left: none;
  padding-left: 0;
  margin-top: var(--space-2);
  position: relative;
  background: transparent;
  border-radius: 0;
  padding-top: 0;
  padding-bottom: var(--space-3);
}

.admin-nav-menu .nav-subitem {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2_5) var(--space-5);
  color: var(--gray-500);
  text-decoration: none;
  border-radius: 0;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-regular);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 0;
  border: none;
  position: relative;
  background: transparent;
  padding-left: calc(var(--space-5) + var(--space-4));
}

.admin-nav-menu .nav-subitem::before {
  content: '';
  position: absolute;
  left: calc(var(--space-5) + var(--space-2));
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  border-radius: var(--radius-full);
  background: var(--gray-400);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-nav-menu .nav-subitem:hover {
  background: rgba(0, 0, 0, 0.02);
  color: var(--gray-700);
  padding-left: calc(var(--space-5) + var(--space-5));
}

.admin-nav-menu .nav-subitem:hover::before {
  background: var(--primary-500);
  transform: translateY(-50%) scale(1.5);
}

.admin-nav-menu .nav-subitem.active {
  background: rgba(99, 102, 241, 0.06);
  color: var(--primary-700);
  font-weight: var(--font-weight-medium);
}

.admin-nav-menu .nav-subitem.active::before {
  background: var(--primary-500);
  transform: translateY(-50%) scale(1.5);
}

.admin-nav-menu .collapsed-menu-icons {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.admin-nav-menu .collapsed-menu-icons .nav-item {
  justify-content: center;
  padding: 12px;
  margin: 0 8px;
}

.admin-nav-menu .collapsed-menu-icons .nav-item.active::before {
  display: none;
}

/* 过渡动画 */
.admin-nav-menu .fade-enter-active,
.admin-nav-menu .fade-leave-active {
  transition: opacity 0.3s ease;
}

.admin-nav-menu .fade-enter-from,
.admin-nav-menu .fade-leave-to {
  opacity: 0;
}
</style>
